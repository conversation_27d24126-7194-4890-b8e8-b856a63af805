import {NestFactory} from '@nestjs/core';
import {AppModule} from './app.module';
import * as express from 'express';

async function bootstrap() {
    const app = await NestFactory.create(AppModule);

    // Configure Express body parser with increased limits for GraphQL requests
    app.use(express.json({ limit: '50mb' }));
    app.use(express.urlencoded({ limit: '50mb', extended: true }));
    // Configure CORS to allow web, mobile, and desktop app origins
    const allowedOrigins = [
        process.env.FRONTEND_URL || 'http://localhost:4545',
        'http://localhost:4545',
        'https://cryptodo.dsserv.de',
        'http://cryptodo.dsserv.de',
        // Tauri app origins
        'tauri://localhost',
        'https://tauri.localhost',
        'http://tauri.localhost',
        // Capacitor app origins
        'capacitor://localhost',
        'ionic://localhost',
        'http://localhost',
        'https://localhost',
    ];

    app.enableCors({
        origin: (origin, callback) => {
            // Allow requests with no origin (like mobile apps, curl requests, or desktop apps)
            if (!origin) return callback(null, true);

            // Allow Tauri and Capacitor app origins
            if (origin && (
                origin.startsWith('tauri://') ||
                origin.startsWith('capacitor://') ||
                origin.startsWith('ionic://') ||
                allowedOrigins.includes(origin)
            )) {
                return callback(null, true);
            }

            console.log('CORS blocked origin:', origin);
            const msg = 'The CORS policy for this site does not allow access from the specified Origin.';
            return callback(new Error(msg), false);
        },
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    });
    await app.listen(4544);
}

bootstrap();

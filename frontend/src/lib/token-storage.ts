// Cross-platform token storage for web browsers and Tauri apps

import Cookies from 'js-cookie';

// Check if we're running in a Tauri app
const isTauri = () => {
  return typeof window !== 'undefined' && 
         (window as any).__TAURI__ !== undefined;
};

// Check if we're running in a Capacitor app
const isCapacitor = () => {
  return typeof window !== 'undefined' && 
         (window as any).Capacitor !== undefined;
};

// Check if we're in a native app environment
const isNativeApp = () => {
  return isTauri() || isCapacitor();
};

export const TokenStorage = {
  // Set authentication token
  setToken: (token: string, expirationDays: number = 7) => {
    console.log('Setting token:', { 
      token: token.substring(0, 20) + '...', 
      isTauri: isTauri(), 
      isCapacitor: isCapacitor(),
      isNativeApp: isNativeApp()
    });

    if (isNativeApp()) {
      // Use localStorage for native apps (<PERSON><PERSON>, Capac<PERSON>)
      try {
        const expirationTime = new Date().getTime() + (expirationDays * 24 * 60 * 60 * 1000);
        const tokenData = {
          token,
          expiration: expirationTime
        };
        localStorage.setItem('auth_token', JSON.stringify(tokenData));
        console.log('Token stored in localStorage for native app');
      } catch (error) {
        console.error('Failed to store token in localStorage:', error);
        // Fallback to cookies
        Cookies.set('token', token, { expires: expirationDays });
      }
    } else {
      // Use cookies for web browsers
      Cookies.set('token', token, { expires: expirationDays });
      console.log('Token stored in cookies for web browser');
    }
  },

  // Get authentication token
  getToken: (): string | null => {
    if (isNativeApp()) {
      // Try localStorage first for native apps
      try {
        const tokenDataStr = localStorage.getItem('auth_token');
        if (tokenDataStr) {
          const tokenData = JSON.parse(tokenDataStr);
          
          // Check if token is expired
          if (tokenData.expiration && new Date().getTime() > tokenData.expiration) {
            console.log('Token expired, removing from localStorage');
            localStorage.removeItem('auth_token');
            return null;
          }
          
          console.log('Token retrieved from localStorage for native app');
          return tokenData.token;
        }
      } catch (error) {
        console.error('Failed to retrieve token from localStorage:', error);
        // Fallback to cookies
      }
      
      // Fallback to cookies for native apps
      const cookieToken = Cookies.get('token');
      if (cookieToken) {
        console.log('Token retrieved from cookies (fallback) for native app');
        return cookieToken;
      }
    } else {
      // Use cookies for web browsers
      const token = Cookies.get('token');
      if (token) {
        console.log('Token retrieved from cookies for web browser');
        return token;
      }
    }

    console.log('No token found');
    return null;
  },

  // Remove authentication token
  removeToken: () => {
    console.log('Removing token from all storage methods');
    
    // Remove from localStorage
    try {
      localStorage.removeItem('auth_token');
      console.log('Token removed from localStorage');
    } catch (error) {
      console.error('Failed to remove token from localStorage:', error);
    }
    
    // Remove from cookies
    Cookies.remove('token');
    console.log('Token removed from cookies');
  },

  // Check if token exists
  hasToken: (): boolean => {
    return TokenStorage.getToken() !== null;
  }
};

export default TokenStorage;

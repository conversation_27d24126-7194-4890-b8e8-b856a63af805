{"rustc": 13390576987229896279, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 350963729416305046], [10755362358622467486, "build_script_build", false, 17271204852692603819], [7236291379133587555, "build_script_build", false, 13853619385969164604]], "local": [{"RerunIfChanged": {"output": "release/build/app-f7741a300a8cc3ef/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}
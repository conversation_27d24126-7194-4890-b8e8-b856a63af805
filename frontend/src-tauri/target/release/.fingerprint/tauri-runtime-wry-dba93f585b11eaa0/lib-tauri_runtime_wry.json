{"rustc": 13390576987229896279, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 4412240653466374978, "deps": [[442785307232013896, "tauri_runtime", false, 10728696839774334898], [1386409696764982933, "objc2", false, 682772382704846403], [3150220818285335163, "url", false, 14631689195943820765], [4143744114649553716, "raw_window_handle", false, 6165104091461141771], [5986029879202738730, "log", false, 9091094003140627871], [7752760652095876438, "build_script_build", false, 7275218381538674310], [9010263965687315507, "http", false, 6999963589542305595], [9859211262912517217, "objc2_foundation", false, 684425966284465111], [10575598148575346675, "objc2_app_kit", false, 7915877278646701602], [11050281405049894993, "tauri_utils", false, 1844257209738294557], [13223659721939363523, "tao", false, 6846286500639575812], [14794439852947137341, "wry", false, 15453355621481809725]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-runtime-wry-dba93f585b11eaa0/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
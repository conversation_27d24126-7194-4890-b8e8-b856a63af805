{"rustc": 13390576987229896279, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 1369601567987815722, "path": 16684189649581690262, "deps": [[4899080583175475170, "semver", false, 8670465836850777564], [6913375703034175521, "schemars", false, 8426313364049993862], [7170110829644101142, "json_patch", false, 17942632113848897521], [8786711029710048183, "toml", false, 7591834184260489740], [9689903380558560274, "serde", false, 9016932716167445635], [11050281405049894993, "tauri_utils", false, 13017321240123997546], [12714016054753183456, "tauri_winres", false, 11494418899536825861], [13077543566650298139, "heck", false, 9215854086736878206], [13475171727366188400, "cargo_toml", false, 16178273898934404706], [13625485746686963219, "anyhow", false, 4216398811697898183], [15367738274754116744, "serde_json", false, 1654003629016208561], [15622660310229662834, "walkdir", false, 13106667824026078141], [16928111194414003569, "dirs", false, 1407861719433112076], [17155886227862585100, "glob", false, 7927218581892005826]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-build-e0fdbdd6377d0af6/dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
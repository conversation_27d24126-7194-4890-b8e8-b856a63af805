{"rustc": 13390576987229896279, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 13012298459997416127, "deps": [[3060637413840920116, "proc_macro2", false, 5496256113606164819], [3150220818285335163, "url", false, 3060453165440995957], [4767930184903566869, "plist", false, 3095791420265655355], [4899080583175475170, "semver", false, 8670465836850777564], [7170110829644101142, "json_patch", false, 17942632113848897521], [7392050791754369441, "ico", false, 6721107816452902047], [8319709847752024821, "uuid", false, 7334899135908550991], [9689903380558560274, "serde", false, 9016932716167445635], [9857275760291862238, "sha2", false, 11487057751865841625], [10806645703491011684, "thiserror", false, 7250584663553972476], [11050281405049894993, "tauri_utils", false, 13017321240123997546], [12409575957772518135, "time", false, 16325417161489790419], [12687914511023397207, "png", false, 15357534591801355596], [13077212702700853852, "base64", false, 17970237313482183], [14132538657330703225, "brotli", false, 2535132188018064460], [15367738274754116744, "serde_json", false, 1654003629016208561], [15622660310229662834, "walkdir", false, 13106667824026078141], [17990358020177143287, "quote", false, 4960977376643775661], [18149961000318489080, "syn", false, 3476095006022478446]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-codegen-0b4be92177e5a3af/dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
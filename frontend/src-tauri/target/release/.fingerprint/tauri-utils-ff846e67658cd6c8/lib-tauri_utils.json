{"rustc": 13390576987229896279, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 1369601567987815722, "path": 1007369222557096012, "deps": [[561782849581144631, "html5ever", false, 16608142079681898908], [1200537532907108615, "url<PERSON><PERSON>n", false, 14975687023852621481], [3060637413840920116, "proc_macro2", false, 5496256113606164819], [3129130049864710036, "memchr", false, 12471573543025617926], [3150220818285335163, "url", false, 3060453165440995957], [3191507132440681679, "serde_untagged", false, 13929321433112529632], [4899080583175475170, "semver", false, 8670465836850777564], [5986029879202738730, "log", false, 2685986805761583032], [6213549728662707793, "serde_with", false, 11405597620424732909], [6262254372177975231, "kuchiki", false, 12641763394491138998], [6606131838865521726, "ctor", false, 5654407610330888252], [6913375703034175521, "schemars", false, 8426313364049993862], [7170110829644101142, "json_patch", false, 17942632113848897521], [8319709847752024821, "uuid", false, 7334899135908550991], [8786711029710048183, "toml", false, 7591834184260489740], [9010263965687315507, "http", false, 12530400690021098027], [9451456094439810778, "regex", false, 14744424607037433784], [9689903380558560274, "serde", false, 9016932716167445635], [10806645703491011684, "thiserror", false, 7250584663553972476], [11655476559277113544, "cargo_metadata", false, 5595051347096027780], [11989259058781683633, "dunce", false, 4673092905132476453], [13625485746686963219, "anyhow", false, 4216398811697898183], [14132538657330703225, "brotli", false, 2535132188018064460], [14885200901422974105, "swift_rs", false, 6685806080805126340], [15367738274754116744, "serde_json", false, 1654003629016208561], [15622660310229662834, "walkdir", false, 13106667824026078141], [17146114186171651583, "infer", false, 4650785424574339786], [17155886227862585100, "glob", false, 7927218581892005826], [17186037756130803222, "phf", false, 13956042068595645616], [17990358020177143287, "quote", false, 4960977376643775661]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-utils-ff846e67658cd6c8/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
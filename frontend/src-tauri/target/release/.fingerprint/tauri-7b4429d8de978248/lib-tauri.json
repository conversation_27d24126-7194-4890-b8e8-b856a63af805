{"rustc": 13390576987229896279, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 4574437020062093592, "deps": [[40386456601120721, "percent_encoding", false, 11126707304200235839], [442785307232013896, "tauri_runtime", false, 10728696839774334898], [1200537532907108615, "url<PERSON><PERSON>n", false, 13516739259162922179], [1386409696764982933, "objc2", false, 682772382704846403], [3150220818285335163, "url", false, 14631689195943820765], [4143744114649553716, "raw_window_handle", false, 6165104091461141771], [4341921533227644514, "muda", false, 11070093255275112024], [4767930184903566869, "plist", false, 7416622322846153382], [4919829919303820331, "serialize_to_javascript", false, 3572395769088389555], [5986029879202738730, "log", false, 9091094003140627871], [7752760652095876438, "tauri_runtime_wry", false, 10713108484383385503], [8589231650440095114, "embed_plist", false, 3087422122911321442], [9010263965687315507, "http", false, 6999963589542305595], [9228235415475680086, "tauri_macros", false, 4305265878385393435], [9538054652646069845, "tokio", false, 205530991738142915], [9689903380558560274, "serde", false, 9849229957246389346], [9859211262912517217, "objc2_foundation", false, 684425966284465111], [9920160576179037441, "getrandom", false, 14699817283199862389], [10229185211513642314, "mime", false, 7245958624105141250], [10575598148575346675, "objc2_app_kit", false, 7915877278646701602], [10629569228670356391, "futures_util", false, 7527718674712040803], [10755362358622467486, "build_script_build", false, 17271204852692603819], [10806645703491011684, "thiserror", false, 13409829239507315341], [11050281405049894993, "tauri_utils", false, 1844257209738294557], [11989259058781683633, "dunce", false, 14249726191681744255], [12565293087094287914, "window_vibrancy", false, 16980301199193609003], [12986574360607194341, "serde_repr", false, 14744183618332785761], [13077543566650298139, "heck", false, 13492876763823103599], [13625485746686963219, "anyhow", false, 14235742050840194296], [15367738274754116744, "serde_json", false, 13651792705707714914], [16928111194414003569, "dirs", false, 6498636822276813166], [17155886227862585100, "glob", false, 10847883223141167492]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-7b4429d8de978248/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 13390576987229896279, "features": "[\"build\"]", "declared_features": "[\"build\", \"runtime\"]", "target": 15996119522804316622, "profile": 1369601567987815722, "path": 6034152179670707720, "deps": [[4767930184903566869, "plist", false, 3095791420265655355], [6913375703034175521, "schemars", false, 8426313364049993862], [8786711029710048183, "toml", false, 7591834184260489740], [9689903380558560274, "serde", false, 9016932716167445635], [11050281405049894993, "tauri_utils", false, 13017321240123997546], [13625485746686963219, "anyhow", false, 4216398811697898183], [15367738274754116744, "serde_json", false, 1654003629016208561], [15622660310229662834, "walkdir", false, 13106667824026078141], [17155886227862585100, "glob", false, 7927218581892005826]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-plugin-7659163eebfe5c4c/dep-lib-tauri_plugin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
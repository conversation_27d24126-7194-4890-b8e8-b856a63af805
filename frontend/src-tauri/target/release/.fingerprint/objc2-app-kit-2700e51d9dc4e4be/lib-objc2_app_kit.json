{"rustc": 13390576987229896279, "features": "[\"AppKitDefines\", \"AppKitErrors\", \"NSATSTypesetter\", \"NSAccessibility\", \"NSAccessibilityColor\", \"NSAccessibilityConstants\", \"NSAccessibilityCustomAction\", \"NSAccessibilityCustomRotor\", \"NSAccessibilityElement\", \"NSAccessibilityProtocols\", \"NSActionCell\", \"NSAdaptiveImageGlyph\", \"NSAffineTransform\", \"NSAlert\", \"NSAlignmentFeedbackFilter\", \"NSAnimation\", \"NSAnimationContext\", \"NSAppearance\", \"NSAppleScriptExtensions\", \"NSApplication\", \"NSApplicationScripting\", \"NSArrayController\", \"NSAttributedString\", \"NSBezierPath\", \"NSBitmapImageRep\", \"NSBox\", \"NSBrowser\", \"NSBrowserCell\", \"NSButton\", \"NSButtonCell\", \"NSButtonTouchBarItem\", \"NSCIImageRep\", \"NSCachedImageRep\", \"NSCandidateListTouchBarItem\", \"NSCell\", \"NSClickGestureRecognizer\", \"NSClipView\", \"NSCollectionView\", \"NSCollectionViewCompositionalLayout\", \"NSCollectionViewFlowLayout\", \"NSCollectionViewGridLayout\", \"NSCollectionViewLayout\", \"NSCollectionViewTransitionLayout\", \"NSColor\", \"NSColorList\", \"NSColorPanel\", \"NSColorPicker\", \"NSColorPickerTouchBarItem\", \"NSColorPicking\", \"NSColorSampler\", \"NSColorSpace\", \"NSColorWell\", \"NSComboBox\", \"NSComboBoxCell\", \"NSComboButton\", \"NSControl\", \"NSController\", \"NSCursor\", \"NSCustomImageRep\", \"NSCustomTouchBarItem\", \"NSDataAsset\", \"NSDatePicker\", \"NSDatePickerCell\", \"NSDictionaryController\", \"NSDiffableDataSource\", \"NSDirection\", \"NSDockTile\", \"NSDocument\", \"NSDocumentController\", \"NSDocumentScripting\", \"NSDragging\", \"NSDraggingItem\", \"NSDraggingSession\", \"NSDrawer\", \"NSEPSImageRep\", \"NSErrors\", \"NSEvent\", \"NSFilePromiseProvider\", \"NSFilePromiseReceiver\", \"NSFileWrapperExtensions\", \"NSFont\", \"NSFontAssetRequest\", \"NSFontCollection\", \"NSFontDescriptor\", \"NSFontManager\", \"NSFontPanel\", \"NSForm\", \"NSFormCell\", \"NSGestureRecognizer\", \"NSGlyphGenerator\", \"NSGlyphInfo\", \"NSGradient\", \"NSGraphics\", \"NSGraphicsContext\", \"NSGridView\", \"NSGroupTouchBarItem\", \"NSHapticFeedback\", \"NSHelpManager\", \"NSImage\", \"NSImageCell\", \"NSImageRep\", \"NSImageView\", \"NSInputManager\", \"NSInputServer\", \"NSInterfaceStyle\", \"NSItemProvider\", \"NSKeyValueBinding\", \"NSLayoutAnchor\", \"NSLayoutConstraint\", \"NSLayoutGuide\", \"NSLayoutManager\", \"NSLevelIndicator\", \"NSLevelIndicatorCell\", \"NSMagnificationGestureRecognizer\", \"NSMatrix\", \"NSMediaLibraryBrowserController\", \"NSMenu\", \"NSMenuItem\", \"NSMenuItemBadge\", \"NSMenuItemCell\", \"NSMenuToolbarItem\", \"NSMovie\", \"NSNib\", \"NSNibConnector\", \"NSNibControlConnector\", \"NSNibDeclarations\", \"NSNibLoading\", \"NSNibOutletConnector\", \"NSObjectController\", \"NSOpenGL\", \"NSOpenGLLayer\", \"NSOpenGLView\", \"NSOpenPanel\", \"NSOutlineView\", \"NSPDFImageRep\", \"NSPDFInfo\", \"NSPDFPanel\", \"NSPICTImageRep\", \"NSPageController\", \"NSPageLayout\", \"NSPanGestureRecognizer\", \"NSPanel\", \"NSParagraphStyle\", \"NSPasteboard\", \"NSPasteboardItem\", \"NSPathCell\", \"NSPathComponentCell\", \"NSPathControl\", \"NSPathControlItem\", \"NSPersistentDocument\", \"NSPickerTouchBarItem\", \"NSPopUpButton\", \"NSPopUpButtonCell\", \"NSPopover\", \"NSPopoverTouchBarItem\", \"NSPredicateEditor\", \"NSPredicateEditorRowTemplate\", \"NSPressGestureRecognizer\", \"NSPressureConfiguration\", \"NSPreviewRepresentingActivityItem\", \"NSPrintInfo\", \"NSPrintOperation\", \"NSPrintPanel\", \"NSPrinter\", \"NSProgressIndicator\", \"NSResponder\", \"NSRotationGestureRecognizer\", \"NSRuleEditor\", \"NSRulerMarker\", \"NSRulerView\", \"NSRunningApplication\", \"NSSavePanel\", \"NSScreen\", \"NSScrollView\", \"NSScroller\", \"NSScrubber\", \"NSScrubberItemView\", \"NSScrubberLayout\", \"NSSearchField\", \"NSSearchFieldCell\", \"NSSearchToolbarItem\", \"NSSecureTextField\", \"NSSegmentedCell\", \"NSSegmentedControl\", \"NSShadow\", \"NSSharingCollaborationModeRestriction\", \"NSSharingService\", \"NSSharingServicePickerToolbarItem\", \"NSSharingServicePickerTouchBarItem\", \"NSSlider\", \"NSSliderAccessory\", \"NSSliderCell\", \"NSSliderTouchBarItem\", \"NSSound\", \"NSSpeechRecognizer\", \"NSSpeechSynthesizer\", \"NSSpellChecker\", \"NSSpellProtocol\", \"NSSplitView\", \"NSSplitViewController\", \"NSSplitViewItem\", \"NSStackView\", \"NSStatusBar\", \"NSStatusBarButton\", \"NSStatusItem\", \"NSStepper\", \"NSStepperCell\", \"NSStepperTouchBarItem\", \"NSStoryboard\", \"NSStoryboardSegue\", \"NSStringDrawing\", \"NSSwitch\", \"NSTabView\", \"NSTabViewController\", \"NSTabViewItem\", \"NSTableCellView\", \"NSTableColumn\", \"NSTableHeaderCell\", \"NSTableHeaderView\", \"NSTableRowView\", \"NSTableView\", \"NSTableViewDiffableDataSource\", \"NSTableViewRowAction\", \"NSText\", \"NSTextAlternatives\", \"NSTextAttachment\", \"NSTextAttachmentCell\", \"NSTextCheckingClient\", \"NSTextCheckingController\", \"NSTextContainer\", \"NSTextContent\", \"NSTextContentManager\", \"NSTextElement\", \"NSTextField\", \"NSTextFieldCell\", \"NSTextFinder\", \"NSTextInputClient\", \"NSTextInputContext\", \"NSTextInsertionIndicator\", \"NSTextLayoutFragment\", \"NSTextLayoutManager\", \"NSTextLineFragment\", \"NSTextList\", \"NSTextListElement\", \"NSTextRange\", \"NSTextSelection\", \"NSTextSelectionNavigation\", \"NSTextStorage\", \"NSTextStorageScripting\", \"NSTextTable\", \"NSTextView\", \"NSTextViewportLayoutController\", \"NSTintConfiguration\", \"NSTitlebarAccessoryViewController\", \"NSTokenField\", \"NSTokenFieldCell\", \"NSToolbar\", \"NSToolbarItem\", \"NSToolbarItemGroup\", \"NSTouch\", \"NSTouchBar\", \"NSTouchBarItem\", \"NSTrackingArea\", \"NSTrackingSeparatorToolbarItem\", \"NSTreeController\", \"NSTreeNode\", \"NSTypesetter\", \"NSUserActivity\", \"NSUserDefaultsController\", \"NSUserInterfaceCompression\", \"NSUserInterfaceItemIdentification\", \"NSUserInterfaceItemSearching\", \"NSUserInterfaceLayout\", \"NSUserInterfaceValidation\", \"NSView\", \"NSViewController\", \"NSVisualEffectView\", \"NSWindow\", \"NSWindowController\", \"NSWindowRestoration\", \"NSWindowScripting\", \"NSWindowTab\", \"NSWindowTabGroup\", \"NSWorkspace\", \"NSWritingToolsCoordinator\", \"NSWritingToolsCoordinatorAnimationParameters\", \"NSWritingToolsCoordinatorContext\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"libc\", \"objc2-cloud-kit\", \"objc2-core-data\", \"objc2-core-foundation\", \"objc2-core-graphics\", \"objc2-core-image\", \"objc2-quartz-core\", \"std\"]", "declared_features": "[\"AppKitDefines\", \"AppKitErrors\", \"NSATSTypesetter\", \"NSAccessibility\", \"NSAccessibilityColor\", \"NSAccessibilityConstants\", \"NSAccessibilityCustomAction\", \"NSAccessibilityCustomRotor\", \"NSAccessibilityElement\", \"NSAccessibilityProtocols\", \"NSActionCell\", \"NSAdaptiveImageGlyph\", \"NSAffineTransform\", \"NSAlert\", \"NSAlignmentFeedbackFilter\", \"NSAnimation\", \"NSAnimationContext\", \"NSAppearance\", \"NSAppleScriptExtensions\", \"NSApplication\", \"NSApplicationScripting\", \"NSArrayController\", \"NSAttributedString\", \"NSBezierPath\", \"NSBitmapImageRep\", \"NSBox\", \"NSBrowser\", \"NSBrowserCell\", \"NSButton\", \"NSButtonCell\", \"NSButtonTouchBarItem\", \"NSCIImageRep\", \"NSCachedImageRep\", \"NSCandidateListTouchBarItem\", \"NSCell\", \"NSClickGestureRecognizer\", \"NSClipView\", \"NSCollectionView\", \"NSCollectionViewCompositionalLayout\", \"NSCollectionViewFlowLayout\", \"NSCollectionViewGridLayout\", \"NSCollectionViewLayout\", \"NSCollectionViewTransitionLayout\", \"NSColor\", \"NSColorList\", \"NSColorPanel\", \"NSColorPicker\", \"NSColorPickerTouchBarItem\", \"NSColorPicking\", \"NSColorSampler\", \"NSColorSpace\", \"NSColorWell\", \"NSComboBox\", \"NSComboBoxCell\", \"NSComboButton\", \"NSControl\", \"NSController\", \"NSCursor\", \"NSCustomImageRep\", \"NSCustomTouchBarItem\", \"NSDataAsset\", \"NSDatePicker\", \"NSDatePickerCell\", \"NSDictionaryController\", \"NSDiffableDataSource\", \"NSDirection\", \"NSDockTile\", \"NSDocument\", \"NSDocumentController\", \"NSDocumentScripting\", \"NSDragging\", \"NSDraggingItem\", \"NSDraggingSession\", \"NSDrawer\", \"NSEPSImageRep\", \"NSErrors\", \"NSEvent\", \"NSFilePromiseProvider\", \"NSFilePromiseReceiver\", \"NSFileWrapperExtensions\", \"NSFont\", \"NSFontAssetRequest\", \"NSFontCollection\", \"NSFontDescriptor\", \"NSFontManager\", \"NSFontPanel\", \"NSForm\", \"NSFormCell\", \"NSGestureRecognizer\", \"NSGlyphGenerator\", \"NSGlyphInfo\", \"NSGradient\", \"NSGraphics\", \"NSGraphicsContext\", \"NSGridView\", \"NSGroupTouchBarItem\", \"NSHapticFeedback\", \"NSHelpManager\", \"NSImage\", \"NSImageCell\", \"NSImageRep\", \"NSImageView\", \"NSInputManager\", \"NSInputServer\", \"NSInterfaceStyle\", \"NSItemProvider\", \"NSKeyValueBinding\", \"NSLayoutAnchor\", \"NSLayoutConstraint\", \"NSLayoutGuide\", \"NSLayoutManager\", \"NSLevelIndicator\", \"NSLevelIndicatorCell\", \"NSMagnificationGestureRecognizer\", \"NSMatrix\", \"NSMediaLibraryBrowserController\", \"NSMenu\", \"NSMenuItem\", \"NSMenuItemBadge\", \"NSMenuItemCell\", \"NSMenuToolbarItem\", \"NSMovie\", \"NSNib\", \"NSNibConnector\", \"NSNibControlConnector\", \"NSNibDeclarations\", \"NSNibLoading\", \"NSNibOutletConnector\", \"NSObjectController\", \"NSOpenGL\", \"NSOpenGLLayer\", \"NSOpenGLView\", \"NSOpenPanel\", \"NSOutlineView\", \"NSPDFImageRep\", \"NSPDFInfo\", \"NSPDFPanel\", \"NSPICTImageRep\", \"NSPageController\", \"NSPageLayout\", \"NSPanGestureRecognizer\", \"NSPanel\", \"NSParagraphStyle\", \"NSPasteboard\", \"NSPasteboardItem\", \"NSPathCell\", \"NSPathComponentCell\", \"NSPathControl\", \"NSPathControlItem\", \"NSPersistentDocument\", \"NSPickerTouchBarItem\", \"NSPopUpButton\", \"NSPopUpButtonCell\", \"NSPopover\", \"NSPopoverTouchBarItem\", \"NSPredicateEditor\", \"NSPredicateEditorRowTemplate\", \"NSPressGestureRecognizer\", \"NSPressureConfiguration\", \"NSPreviewRepresentingActivityItem\", \"NSPrintInfo\", \"NSPrintOperation\", \"NSPrintPanel\", \"NSPrinter\", \"NSProgressIndicator\", \"NSResponder\", \"NSRotationGestureRecognizer\", \"NSRuleEditor\", \"NSRulerMarker\", \"NSRulerView\", \"NSRunningApplication\", \"NSSavePanel\", \"NSScreen\", \"NSScrollView\", \"NSScroller\", \"NSScrubber\", \"NSScrubberItemView\", \"NSScrubberLayout\", \"NSSearchField\", \"NSSearchFieldCell\", \"NSSearchToolbarItem\", \"NSSecureTextField\", \"NSSegmentedCell\", \"NSSegmentedControl\", \"NSShadow\", \"NSSharingCollaborationModeRestriction\", \"NSSharingService\", \"NSSharingServicePickerToolbarItem\", \"NSSharingServicePickerTouchBarItem\", \"NSSlider\", \"NSSliderAccessory\", \"NSSliderCell\", \"NSSliderTouchBarItem\", \"NSSound\", \"NSSpeechRecognizer\", \"NSSpeechSynthesizer\", \"NSSpellChecker\", \"NSSpellProtocol\", \"NSSplitView\", \"NSSplitViewController\", \"NSSplitViewItem\", \"NSStackView\", \"NSStatusBar\", \"NSStatusBarButton\", \"NSStatusItem\", \"NSStepper\", \"NSStepperCell\", \"NSStepperTouchBarItem\", \"NSStoryboard\", \"NSStoryboardSegue\", \"NSStringDrawing\", \"NSSwitch\", \"NSTabView\", \"NSTabViewController\", \"NSTabViewItem\", \"NSTableCellView\", \"NSTableColumn\", \"NSTableHeaderCell\", \"NSTableHeaderView\", \"NSTableRowView\", \"NSTableView\", \"NSTableViewDiffableDataSource\", \"NSTableViewRowAction\", \"NSText\", \"NSTextAlternatives\", \"NSTextAttachment\", \"NSTextAttachmentCell\", \"NSTextCheckingClient\", \"NSTextCheckingController\", \"NSTextContainer\", \"NSTextContent\", \"NSTextContentManager\", \"NSTextElement\", \"NSTextField\", \"NSTextFieldCell\", \"NSTextFinder\", \"NSTextInputClient\", \"NSTextInputContext\", \"NSTextInsertionIndicator\", \"NSTextLayoutFragment\", \"NSTextLayoutManager\", \"NSTextLineFragment\", \"NSTextList\", \"NSTextListElement\", \"NSTextRange\", \"NSTextSelection\", \"NSTextSelectionNavigation\", \"NSTextStorage\", \"NSTextStorageScripting\", \"NSTextTable\", \"NSTextView\", \"NSTextViewportLayoutController\", \"NSTintConfiguration\", \"NSTitlebarAccessoryViewController\", \"NSTokenField\", \"NSTokenFieldCell\", \"NSToolbar\", \"NSToolbarItem\", \"NSToolbarItemGroup\", \"NSTouch\", \"NSTouchBar\", \"NSTouchBarItem\", \"NSTrackingArea\", \"NSTrackingSeparatorToolbarItem\", \"NSTreeController\", \"NSTreeNode\", \"NSTypesetter\", \"NSUserActivity\", \"NSUserDefaultsController\", \"NSUserInterfaceCompression\", \"NSUserInterfaceItemIdentification\", \"NSUserInterfaceItemSearching\", \"NSUserInterfaceLayout\", \"NSUserInterfaceValidation\", \"NSView\", \"NSViewController\", \"NSVisualEffectView\", \"NSWindow\", \"NSWindowController\", \"NSWindowRestoration\", \"NSWindowScripting\", \"NSWindowTab\", \"NSWindowTabGroup\", \"NSWorkspace\", \"NSWritingToolsCoordinator\", \"NSWritingToolsCoordinatorAnimationParameters\", \"NSWritingToolsCoordinatorContext\", \"alloc\", \"bitflags\", \"block2\", \"default\", \"gnustep-1-7\", \"gnustep-1-8\", \"gnustep-1-9\", \"gnustep-2-0\", \"gnustep-2-1\", \"libc\", \"objc2-cloud-kit\", \"objc2-core-data\", \"objc2-core-foundation\", \"objc2-core-graphics\", \"objc2-core-image\", \"objc2-quartz-core\", \"objc2-uniform-type-identifiers\", \"std\"]", "target": 18041863106907093401, "profile": 10457427126514475017, "path": 16714013982249342685, "deps": [[309970253587158206, "block2", false, 17426827172319810192], [1386409696764982933, "objc2", false, 682772382704846403], [1517964206604611491, "objc2_quartz_core", false, 6175475190491093340], [2924422107542798392, "libc", false, 10973123659651334227], [7828294911682607782, "objc2_core_graphics", false, 9300377199079118946], [7896293946984509699, "bitflags", false, 6269608893195009809], [9859211262912517217, "objc2_foundation", false, 684425966284465111], [10378802769730441691, "objc2_core_foundation", false, 6425208410289907623], [14310139767743755709, "objc2_core_image", false, 7028460730542995098], [15131121180101279514, "objc2_core_data", false, 11021208464532461242], [15796387676521131728, "objc2_cloud_kit", false, 7902606528376414524]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/objc2-app-kit-2700e51d9dc4e4be/dep-lib-objc2_app_kit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
{"rustc": 13390576987229896279, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 2331262289216263913, "deps": [[3060637413840920116, "proc_macro2", false, 5496256113606164819], [7341521034400937459, "tauri_codegen", false, 11533168838055361906], [11050281405049894993, "tauri_utils", false, 13017321240123997546], [13077543566650298139, "heck", false, 9215854086736878206], [17990358020177143287, "quote", false, 4960977376643775661], [18149961000318489080, "syn", false, 3476095006022478446]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-macros-518d779d0dd17ce4/dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
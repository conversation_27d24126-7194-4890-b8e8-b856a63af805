{"rustc": 13390576987229896279, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 5519143596889783332, "deps": [[442785307232013896, "build_script_build", false, 9891572505160658117], [3150220818285335163, "url", false, 14631689195943820765], [4143744114649553716, "raw_window_handle", false, 6165104091461141771], [7606335748176206944, "dpi", false, 14833134325961696195], [9010263965687315507, "http", false, 6999963589542305595], [9689903380558560274, "serde", false, 9849229957246389346], [10806645703491011684, "thiserror", false, 13409829239507315341], [11050281405049894993, "tauri_utils", false, 1844257209738294557], [15367738274754116744, "serde_json", false, 13651792705707714914], [16727543399706004146, "cookie", false, 18296627378276830673]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tauri-runtime-2b7046c15cf76ebb/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
["/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/path/autogenerated/commands/basename.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/path/autogenerated/commands/dirname.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/path/autogenerated/commands/extname.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/path/autogenerated/commands/is_absolute.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/path/autogenerated/commands/join.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/path/autogenerated/commands/normalize.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/path/autogenerated/commands/resolve.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/path/autogenerated/commands/resolve_directory.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/path/autogenerated/default.toml"]
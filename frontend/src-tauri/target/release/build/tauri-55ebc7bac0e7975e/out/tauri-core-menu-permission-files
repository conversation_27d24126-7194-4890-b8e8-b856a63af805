["/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/append.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/create_default.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/get.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/insert.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/is_checked.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/is_enabled.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/items.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/new.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/popup.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/prepend.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/remove.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/remove_at.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/set_accelerator.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/set_as_app_menu.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/set_as_help_menu_for_nsapp.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/set_as_window_menu.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/set_as_windows_menu_for_nsapp.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/set_checked.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/set_enabled.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/set_icon.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/set_text.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/commands/text.toml", "/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/tauri-55ebc7bac0e7975e/out/permissions/menu/autogenerated/default.toml"]
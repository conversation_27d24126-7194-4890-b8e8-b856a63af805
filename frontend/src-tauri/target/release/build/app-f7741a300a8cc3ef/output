cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rerun-if-changed=/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=app
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=com_cryptodo
cargo:rustc-check-cfg=cfg(dev)
cargo:PERMISSION_FILES_PATH=/Users/<USER>/Coden/crypto/cryptodo/frontend/src-tauri/target/release/build/app-f7741a300a8cc3ef/out/app-manifest/__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=x86_64-apple-darwin
cargo:rustc-env=MACOSX_DEPLOYMENT_TARGET=10.13

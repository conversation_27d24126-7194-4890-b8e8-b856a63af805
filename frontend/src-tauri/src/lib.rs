use std::collections::HashMap;
use std::sync::Mutex;
use tauri::State;

// Simple in-memory storage for tokens
type TokenStorage = Mutex<HashMap<String, String>>;

#[tauri::command]
fn store_token(key: String, value: String, storage: State<TokenStorage>) -> Result<(), String> {
    let mut store = storage.lock().map_err(|e| e.to_string())?;
    store.insert(key, value);
    log::info!("Token stored successfully");
    Ok(())
}

#[tauri::command]
fn get_token(key: String, storage: State<TokenStorage>) -> Result<Option<String>, String> {
    let store = storage.lock().map_err(|e| e.to_string())?;
    let token = store.get(&key).cloned();
    log::info!("Token retrieved: {}", token.is_some());
    Ok(token)
}

#[tauri::command]
fn remove_token(key: String, storage: State<TokenStorage>) -> Result<(), String> {
    let mut store = storage.lock().map_err(|e| e.to_string())?;
    store.remove(&key);
    log::info!("Token removed successfully");
    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .manage(TokenStorage::default())
    .invoke_handler(tauri::generate_handler![store_token, get_token, remove_token])
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
      }
      Ok(())
    })
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}

(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[908],{1201:function(e,t,r){"use strict";r.d(t,{kZ:function(){return GoogleLogin},rg:function(){return GoogleOAuth<PERSON>rovider}});var n=r(5271);let i=(0,n.createContext)(null);function GoogleOAuthProvider({clientId:e,nonce:t,onScriptLoadSuccess:r,onScriptLoadError:o,children:a}){let s=function(e={}){let{nonce:t,onScriptLoadSuccess:r,onScriptLoadError:i}=e,[o,a]=(0,n.useState)(!1),s=(0,n.useRef)(r);s.current=r;let u=(0,n.useRef)(i);return u.current=i,(0,n.useEffect)(()=>{let e=document.createElement("script");return e.src="https://accounts.google.com/gsi/client",e.async=!0,e.defer=!0,e.nonce=t,e.onload=()=>{var e;a(!0),null===(e=s.current)||void 0===e||e.call(s)},e.onerror=()=>{var e;a(!1),null===(e=u.current)||void 0===e||e.call(u)},document.body.appendChild(e),()=>{document.body.removeChild(e)}},[t]),o}({nonce:t,onScriptLoadSuccess:r,onScriptLoadError:o}),u=(0,n.useMemo)(()=>({clientId:e,scriptLoadedSuccessfully:s}),[e,s]);return n.createElement(i.Provider,{value:u},a)}let o={large:40,medium:32,small:20};function GoogleLogin({onSuccess:e,onError:t,useOneTap:r,promptMomentNotification:a,type:s="standard",theme:u="outline",size:l="large",text:d,shape:c,logo_alignment:f,width:p,locale:y,click_listener:m,containerProps:g,...b}){let h=(0,n.useRef)(null),{clientId:v,scriptLoadedSuccessfully:_}=function(){let e=(0,n.useContext)(i);if(!e)throw Error("Google OAuth components must be used within GoogleOAuthProvider");return e}(),S=(0,n.useRef)(e);S.current=e;let O=(0,n.useRef)(t);O.current=t;let w=(0,n.useRef)(a);return w.current=a,(0,n.useEffect)(()=>{var e,t,n,i,o,a,g,V,x;if(_)return null===(n=null===(t=null===(e=null==window?void 0:window.google)||void 0===e?void 0:e.accounts)||void 0===t?void 0:t.id)||void 0===n||n.initialize({client_id:v,callback:e=>{var t;if(!(null==e?void 0:e.credential))return null===(t=O.current)||void 0===t?void 0:t.call(O);let{credential:r,select_by:n}=e;S.current({credential:r,clientId:function(e){var t;let r=null!==(t=null==e?void 0:e.clientId)&&void 0!==t?t:null==e?void 0:e.client_id;return r}(e),select_by:n})},...b}),null===(a=null===(o=null===(i=null==window?void 0:window.google)||void 0===i?void 0:i.accounts)||void 0===o?void 0:o.id)||void 0===a||a.renderButton(h.current,{type:s,theme:u,size:l,text:d,shape:c,logo_alignment:f,width:p,locale:y,click_listener:m}),r&&(null===(x=null===(V=null===(g=null==window?void 0:window.google)||void 0===g?void 0:g.accounts)||void 0===V?void 0:V.id)||void 0===x||x.prompt(w.current)),()=>{var e,t,n;r&&(null===(n=null===(t=null===(e=null==window?void 0:window.google)||void 0===e?void 0:e.accounts)||void 0===t?void 0:t.id)||void 0===n||n.cancel())}},[v,_,r,s,u,l,d,c,f,p,y]),n.createElement("div",{...g,ref:h,style:{height:o[l],...null==g?void 0:g.style}})}},760:function(e,t,r){"use strict";r.d(t,{Oq:function(){return calculateBox},dO:function(){return createBox},jn:function(){return expand},iz:function(){return getBox},Dz:function(){return getRect},cv:function(){return offset},oc:function(){return withScroll}});var getRect=function(e){var t=e.top,r=e.right,n=e.bottom,i=e.left;return{top:t,right:r,bottom:n,left:i,width:r-i,height:n-t,x:i,y:t,center:{x:(r+i)/2,y:(n+t)/2}}},expand=function(e,t){return{top:e.top-t.top,left:e.left-t.left,bottom:e.bottom+t.bottom,right:e.right+t.right}},shrink=function(e,t){return{top:e.top+t.top,left:e.left+t.left,bottom:e.bottom-t.bottom,right:e.right-t.right}},n={top:0,right:0,bottom:0,left:0},createBox=function(e){var t=e.borderBox,r=e.margin,i=void 0===r?n:r,o=e.border,a=void 0===o?n:o,s=e.padding,u=void 0===s?n:s,l=getRect(expand(t,i)),d=getRect(shrink(t,a)),c=getRect(shrink(d,u));return{marginBox:l,borderBox:getRect(t),paddingBox:d,contentBox:c,margin:i,border:a,padding:u}},parse=function(e){var t=e.slice(0,-2);if("px"!==e.slice(-2))return 0;var r=Number(t);return isNaN(r)&&function(e,t){if(!e)throw Error("Invariant failed")}(!1),r},offset=function(e,t){var r=e.borderBox,n=e.border,i=e.margin,o=e.padding;return createBox({borderBox:{top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x},border:n,margin:i,padding:o})},withScroll=function(e,t){return void 0===t&&(t={x:window.pageXOffset,y:window.pageYOffset}),offset(e,t)},calculateBox=function(e,t){return createBox({borderBox:e,margin:{top:parse(t.marginTop),right:parse(t.marginRight),bottom:parse(t.marginBottom),left:parse(t.marginLeft)},padding:{top:parse(t.paddingTop),right:parse(t.paddingRight),bottom:parse(t.paddingBottom),left:parse(t.paddingLeft)},border:{top:parse(t.borderTopWidth),right:parse(t.borderRightWidth),bottom:parse(t.borderBottomWidth),left:parse(t.borderLeftWidth)}})},getBox=function(e){return calculateBox(e.getBoundingClientRect(),window.getComputedStyle(e))}},2535:function(e,t,r){"use strict";var n=r(6237),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},s={};function getStatics(e){return n.isMemo(e)?a:s[e.$$typeof]||i}s[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s[n.Memo]=a;var u=Object.defineProperty,l=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,c=Object.getOwnPropertyDescriptor,f=Object.getPrototypeOf,p=Object.prototype;e.exports=function hoistNonReactStatics(e,t,r){if("string"!=typeof t){if(p){var n=f(t);n&&n!==p&&hoistNonReactStatics(e,n,r)}var i=l(t);d&&(i=i.concat(d(t)));for(var a=getStatics(e),s=getStatics(t),y=0;y<i.length;++y){var m=i[y];if(!o[m]&&!(r&&r[m])&&!(s&&s[m])&&!(a&&a[m])){var g=c(t,m);try{u(e,m,g)}catch(e){}}}}return e}},7699:function(e,t){"use strict";var r=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function areInputsEqual(e,t){if(e.length!==t.length)return!1;for(var n,i,o=0;o<e.length;o++)if(!((n=e[o])===(i=t[o])||r(n)&&r(i)))return!1;return!0}t.Z=function(e,t){void 0===t&&(t=areInputsEqual);var r,n,i=[],o=!1;return function(){for(var a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];return o&&r===this&&t(a,i)||(n=e.apply(this,a),o=!0,r=this,i=a),n}}},8424:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{noSSR:function(){return noSSR},default:function(){return dynamic}});let n=r(1351),i=(r(5271),n._(r(3997)));function convertModule(e){return{default:(null==e?void 0:e.default)||e}}function noSSR(e,t){return delete t.webpack,delete t.modules,e(t)}function dynamic(e,t){let r=i.default,n={loading:e=>{let{error:t,isLoading:r,pastDelay:n}=e;return null}};e instanceof Promise?n.loader=()=>e:"function"==typeof e?n.loader=e:"object"==typeof e&&(n={...n,...e}),n={...n,...t};let o=n.loader;return(n.loadableGenerated&&(n={...n,...n.loadableGenerated},delete n.loadableGenerated),"boolean"!=typeof n.ssr||n.ssr)?r({...n,loader:()=>null!=o?o().then(convertModule):Promise.resolve(convertModule(()=>null))}):(delete n.webpack,delete n.modules,noSSR(r,n))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1870:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"LoadableContext",{enumerable:!0,get:function(){return o}});let n=r(1351),i=n._(r(5271)),o=i.default.createContext(null)},3997:function(e,t,r){"use strict";/**
@copyright (c) 2017-present James Kyle <<EMAIL>>
 MIT License
 Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:
 The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.
 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE
*/Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let n=r(1351),i=n._(r(5271)),o=r(1870),a=[],s=[],u=!1;function load(e){let t=e(),r={loading:!0,loaded:null,error:null};return r.promise=t.then(e=>(r.loading=!1,r.loaded=e,e)).catch(e=>{throw r.loading=!1,r.error=e,e}),r}let LoadableSubscription=class LoadableSubscription{promise(){return this._res.promise}retry(){this._clearTimeouts(),this._res=this._loadFn(this._opts.loader),this._state={pastDelay:!1,timedOut:!1};let{_res:e,_opts:t}=this;e.loading&&("number"==typeof t.delay&&(0===t.delay?this._state.pastDelay=!0:this._delay=setTimeout(()=>{this._update({pastDelay:!0})},t.delay)),"number"==typeof t.timeout&&(this._timeout=setTimeout(()=>{this._update({timedOut:!0})},t.timeout))),this._res.promise.then(()=>{this._update({}),this._clearTimeouts()}).catch(e=>{this._update({}),this._clearTimeouts()}),this._update({})}_update(e){this._state={...this._state,error:this._res.error,loaded:this._res.loaded,loading:this._res.loading,...e},this._callbacks.forEach(e=>e())}_clearTimeouts(){clearTimeout(this._delay),clearTimeout(this._timeout)}getCurrentValue(){return this._state}subscribe(e){return this._callbacks.add(e),()=>{this._callbacks.delete(e)}}constructor(e,t){this._loadFn=e,this._opts=t,this._callbacks=new Set,this._delay=null,this._timeout=null,this.retry()}};function Loadable(e){return function(e,t){let r=Object.assign({loader:null,loading:null,delay:200,timeout:null,webpack:null,modules:null},t),n=null;function init(){if(!n){let t=new LoadableSubscription(e,r);n={getCurrentValue:t.getCurrentValue.bind(t),subscribe:t.subscribe.bind(t),retry:t.retry.bind(t),promise:t.promise.bind(t)}}return n.promise()}if(!u){let e=r.webpack?r.webpack():r.modules;e&&s.push(t=>{for(let r of e)if(t.includes(r))return init()})}function LoadableComponent(e,t){!function(){init();let e=i.default.useContext(o.LoadableContext);e&&Array.isArray(r.modules)&&r.modules.forEach(t=>{e(t)})}();let a=i.default.useSyncExternalStore(n.subscribe,n.getCurrentValue,n.getCurrentValue);return i.default.useImperativeHandle(t,()=>({retry:n.retry}),[]),i.default.useMemo(()=>{var t;return a.loading||a.error?i.default.createElement(r.loading,{isLoading:a.loading,pastDelay:a.pastDelay,timedOut:a.timedOut,error:a.error,retry:n.retry}):a.loaded?i.default.createElement((t=a.loaded)&&t.default?t.default:t,e):null},[e,a])}return LoadableComponent.preload=()=>init(),LoadableComponent.displayName="LoadableComponent",i.default.forwardRef(LoadableComponent)}(load,e)}function flushInitializers(e,t){let r=[];for(;e.length;){let n=e.pop();r.push(n(t))}return Promise.all(r).then(()=>{if(e.length)return flushInitializers(e,t)})}Loadable.preloadAll=()=>new Promise((e,t)=>{flushInitializers(a).then(e,t)}),Loadable.preloadReady=e=>(void 0===e&&(e=[]),new Promise(t=>{let res=()=>(u=!0,t());flushInitializers(s,e).then(res,res)})),window.__NEXT_PRELOADREADY=Loadable.preloadReady;let l=Loadable},5164:function(){},8909:function(e,t,r){e.exports=r(8424)},8873:function(e,t,r){e.exports=r(6255)},953:function(e,t){"use strict";t.Z=function(e){var t=[],r=null,wrapperFn=function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];t=i,r||(r=requestAnimationFrame(function(){r=null,e.apply(void 0,t)}))};return wrapperFn.cancel=function(){r&&(cancelAnimationFrame(r),r=null)},wrapperFn}},198:function(e,t){"use strict";/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,o=r?Symbol.for("react.fragment"):60107,a=r?Symbol.for("react.strict_mode"):60108,s=r?Symbol.for("react.profiler"):60114,u=r?Symbol.for("react.provider"):60109,l=r?Symbol.for("react.context"):60110,d=r?Symbol.for("react.async_mode"):60111,c=r?Symbol.for("react.concurrent_mode"):60111,f=r?Symbol.for("react.forward_ref"):60112,p=r?Symbol.for("react.suspense"):60113,y=r?Symbol.for("react.suspense_list"):60120,m=r?Symbol.for("react.memo"):60115,g=r?Symbol.for("react.lazy"):60116,b=r?Symbol.for("react.block"):60121,h=r?Symbol.for("react.fundamental"):60117,v=r?Symbol.for("react.responder"):60118,_=r?Symbol.for("react.scope"):60119;function z(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case d:case c:case o:case s:case a:case p:return e;default:switch(e=e&&e.$$typeof){case l:case f:case g:case m:case u:return e;default:return t}}case i:return t}}}function A(e){return z(e)===c}t.AsyncMode=d,t.ConcurrentMode=c,t.ContextConsumer=l,t.ContextProvider=u,t.Element=n,t.ForwardRef=f,t.Fragment=o,t.Lazy=g,t.Memo=m,t.Portal=i,t.Profiler=s,t.StrictMode=a,t.Suspense=p,t.isAsyncMode=function(e){return A(e)||z(e)===d},t.isConcurrentMode=A,t.isContextConsumer=function(e){return z(e)===l},t.isContextProvider=function(e){return z(e)===u},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return z(e)===f},t.isFragment=function(e){return z(e)===o},t.isLazy=function(e){return z(e)===g},t.isMemo=function(e){return z(e)===m},t.isPortal=function(e){return z(e)===i},t.isProfiler=function(e){return z(e)===s},t.isStrictMode=function(e){return z(e)===a},t.isSuspense=function(e){return z(e)===p},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===o||e===c||e===s||e===a||e===p||e===y||"object"==typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===m||e.$$typeof===u||e.$$typeof===l||e.$$typeof===f||e.$$typeof===h||e.$$typeof===v||e.$$typeof===_||e.$$typeof===b)},t.typeOf=z},6237:function(e,t,r){"use strict";e.exports=r(198)},1841:function(e,t){"use strict";/** @license React v17.0.2
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=60103,n=60106,i=60107,o=60108,a=60114,s=60109,u=60110,l=60112,d=60113,c=60120,f=60115,p=60116;if("function"==typeof Symbol&&Symbol.for){var y=Symbol.for;r=y("react.element"),n=y("react.portal"),i=y("react.fragment"),o=y("react.strict_mode"),a=y("react.profiler"),s=y("react.provider"),u=y("react.context"),l=y("react.forward_ref"),d=y("react.suspense"),c=y("react.suspense_list"),f=y("react.memo"),p=y("react.lazy"),y("react.block"),y("react.server.block"),y("react.fundamental"),y("react.debug_trace_mode"),y("react.legacy_hidden")}t.isContextConsumer=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case i:case a:case o:case d:case c:return e;default:switch(e=e&&e.$$typeof){case u:case l:case p:case f:case s:return e;default:return t}}case n:return t}}}(e)===u}},7679:function(e,t,r){"use strict";e.exports=r(1841)},6576:function(e,t,r){"use strict";r.d(t,{zt:function(){return components_Provider},$j:function(){return M}});var n,i,o,a,s,u,l,d,c,f,p,y,m=r(5271),g=m.createContext(null),batch=function(e){e()},b={notify:function(){},get:function(){return[]}};function Subscription_createSubscription(e,t){var r,n=b;function handleChangeWrapper(){i.onStateChange&&i.onStateChange()}function trySubscribe(){if(!r){var i,o,a;r=t?t.addNestedSub(handleChangeWrapper):e.subscribe(handleChangeWrapper),i=batch,o=null,a=null,n={clear:function(){o=null,a=null},notify:function(){i(function(){for(var e=o;e;)e.callback(),e=e.next})},get:function(){for(var e=[],t=o;t;)e.push(t),t=t.next;return e},subscribe:function(e){var t=!0,r=a={callback:e,next:null,prev:a};return r.prev?r.prev.next=r:o=r,function(){t&&null!==o&&(t=!1,r.next?r.next.prev=r.prev:a=r.prev,r.prev?r.prev.next=r.next:o=r.next)}}}}}var i={addNestedSub:function(e){return trySubscribe(),n.subscribe(e)},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:handleChangeWrapper,isSubscribed:function(){return!!r},trySubscribe:trySubscribe,tryUnsubscribe:function(){r&&(r(),r=void 0,n.clear(),n=b)},getListeners:function(){return n}};return i}var h="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?m.useLayoutEffect:m.useEffect,components_Provider=function(e){var t=e.store,r=e.context,n=e.children,i=(0,m.useMemo)(function(){var e=Subscription_createSubscription(t);return{store:t,subscription:e}},[t]),o=(0,m.useMemo)(function(){return t.getState()},[t]);return h(function(){var e=i.subscription;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),o!==t.getState()&&e.notifyNestedSubs(),function(){e.tryUnsubscribe(),e.onStateChange=null}},[i,o]),m.createElement((r||g).Provider,{value:i},n)},v=r(9911);function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}var _=r(2535),S=r.n(_),O=r(7679),w=["getDisplayName","methodName","renderCountProp","shouldHandleStateChanges","storeKey","withRef","forwardRef","context"],V=["reactReduxForwardedRef"],x=[],E=[null,null];function storeStateUpdatesReducer(e,t){var r=e[1];return[t.payload,r+1]}function useIsomorphicLayoutEffectWithArgs(e,t,r){h(function(){return e.apply(void 0,t)},r)}function captureWrapperProps(e,t,r,n,i,o,a){e.current=n,t.current=i,r.current=!1,o.current&&(o.current=null,a())}function subscribeUpdates(e,t,r,n,i,o,a,s,u,l){if(e){var d=!1,c=null,checkForUpdates=function(){if(!d){var e,r,f=t.getState();try{e=n(f,i.current)}catch(e){r=e,c=e}r||(c=null),e===o.current?a.current||u():(o.current=e,s.current=e,a.current=!0,l({type:"STORE_UPDATED",payload:{error:r}}))}};return r.onStateChange=checkForUpdates,r.trySubscribe(),checkForUpdates(),function(){if(d=!0,r.tryUnsubscribe(),r.onStateChange=null,c)throw c}}}var initStateUpdates=function(){return[null,0]};function is(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}function shallowEqual(e,t){if(is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(var i=0;i<r.length;i++)if(!Object.prototype.hasOwnProperty.call(t,r[i])||!is(e[r[i]],t[r[i]]))return!1;return!0}function wrapMapToPropsConstant(e){return function(t,r){var n=e(t,r);function constantSelector(){return n}return constantSelector.dependsOnOwnProps=!1,constantSelector}}function getDependsOnOwnProps(e){return null!==e.dependsOnOwnProps&&void 0!==e.dependsOnOwnProps?!!e.dependsOnOwnProps:1!==e.length}function wrapMapToPropsFunc(e,t){return function(t,r){r.displayName;var proxy=function(e,t){return proxy.dependsOnOwnProps?proxy.mapToProps(e,t):proxy.mapToProps(e)};return proxy.dependsOnOwnProps=!0,proxy.mapToProps=function(t,r){proxy.mapToProps=e,proxy.dependsOnOwnProps=getDependsOnOwnProps(e);var n=proxy(t,r);return"function"==typeof n&&(proxy.mapToProps=n,proxy.dependsOnOwnProps=getDependsOnOwnProps(n),n=proxy(t,r)),n},proxy}}var F=[function(e){return"function"==typeof e?wrapMapToPropsFunc(e,"mapDispatchToProps"):void 0},function(e){return e?void 0:wrapMapToPropsConstant(function(e){return{dispatch:e}})},function(e){return e&&"object"==typeof e?wrapMapToPropsConstant(function(t){return function(e,t){var r={};for(var n in e)!function(n){var i=e[n];"function"==typeof i&&(r[n]=function(){return t(i.apply(void 0,arguments))})}(n);return r}(e,t)}):void 0}],P=[function(e){return"function"==typeof e?wrapMapToPropsFunc(e,"mapStateToProps"):void 0},function(e){return e?void 0:wrapMapToPropsConstant(function(){return{}})}];function defaultMergeProps(e,t,r){return(0,v.Z)({},r,e,t)}var j=[function(e){return"function"==typeof e?function(t,r){r.displayName;var n,i=r.pure,o=r.areMergedPropsEqual,a=!1;return function(t,r,s){var u=e(t,r,s);return a?i&&o(u,n)||(n=u):(a=!0,n=u),n}}:void 0},function(e){return e?void 0:function(){return defaultMergeProps}}],C=["initMapStateToProps","initMapDispatchToProps","initMergeProps"],D=["pure","areStatesEqual","areOwnPropsEqual","areStatePropsEqual","areMergedPropsEqual"];function match(e,t,r){for(var n=t.length-1;n>=0;n--){var i=t[n](e);if(i)return i}return function(t,n){throw Error("Invalid value of type "+typeof e+" for "+r+" argument when connecting component "+n.wrappedComponentName+".")}}function strictEqual(e,t){return e===t}var M=(a=void 0===(o=(i=void 0===n?{}:n).connectHOC)?function(e,t){void 0===t&&(t={});var r=t,n=r.getDisplayName,i=void 0===n?function(e){return"ConnectAdvanced("+e+")"}:n,o=r.methodName,a=void 0===o?"connectAdvanced":o,s=r.renderCountProp,u=void 0===s?void 0:s,l=r.shouldHandleStateChanges,d=void 0===l||l,c=r.storeKey,f=void 0===c?"store":c,p=(r.withRef,r.forwardRef),y=void 0!==p&&p,b=r.context,h=_objectWithoutPropertiesLoose(r,w),_=void 0===b?g:b;return function(t){var r=t.displayName||t.name||"Component",n=i(r),o=(0,v.Z)({},h,{getDisplayName:i,methodName:a,renderCountProp:u,shouldHandleStateChanges:d,storeKey:f,displayName:n,wrappedComponentName:r,WrappedComponent:t}),s=h.pure,l=s?m.useMemo:function(e){return e()};function ConnectFunction(r){var n=(0,m.useMemo)(function(){var e=r.reactReduxForwardedRef,t=_objectWithoutPropertiesLoose(r,V);return[r.context,e,t]},[r]),i=n[0],a=n[1],s=n[2],u=(0,m.useMemo)(function(){return i&&i.Consumer&&(0,O.isContextConsumer)(m.createElement(i.Consumer,null))?i:_},[i,_]),c=(0,m.useContext)(u),f=!!r.store&&!!r.store.getState&&!!r.store.dispatch;c&&c.store;var p=f?r.store:c.store,y=(0,m.useMemo)(function(){return e(p.dispatch,o)},[p]),g=(0,m.useMemo)(function(){if(!d)return E;var e=Subscription_createSubscription(p,f?null:c.subscription),t=e.notifyNestedSubs.bind(e);return[e,t]},[p,f,c]),b=g[0],h=g[1],S=(0,m.useMemo)(function(){return f?c:(0,v.Z)({},c,{subscription:b})},[f,c,b]),w=(0,m.useReducer)(storeStateUpdatesReducer,x,initStateUpdates),F=w[0][0],P=w[1];if(F&&F.error)throw F.error;var j=(0,m.useRef)(),C=(0,m.useRef)(s),D=(0,m.useRef)(),M=(0,m.useRef)(!1),k=l(function(){return D.current&&s===C.current?D.current:y(p.getState(),s)},[p,F,s]);useIsomorphicLayoutEffectWithArgs(captureWrapperProps,[C,j,M,s,k,D,h]),useIsomorphicLayoutEffectWithArgs(subscribeUpdates,[d,p,b,y,C,j,M,D,h,P],[p,b,y]);var T=(0,m.useMemo)(function(){return m.createElement(t,(0,v.Z)({},k,{ref:a}))},[a,t,k]);return(0,m.useMemo)(function(){return d?m.createElement(u.Provider,{value:S},T):T},[u,T,S])}var c=s?m.memo(ConnectFunction):ConnectFunction;if(c.WrappedComponent=t,c.displayName=ConnectFunction.displayName=n,y){var p=m.forwardRef(function(e,t){return m.createElement(c,(0,v.Z)({},e,{reactReduxForwardedRef:t}))});return p.displayName=n,p.WrappedComponent=t,S()(p,t)}return S()(c,t)}}:o,u=void 0===(s=i.mapStateToPropsFactories)?P:s,d=void 0===(l=i.mapDispatchToPropsFactories)?F:l,f=void 0===(c=i.mergePropsFactories)?j:c,y=void 0===(p=i.selectorFactory)?function(e,t){var r=t.initMapStateToProps,n=t.initMapDispatchToProps,i=t.initMergeProps,o=_objectWithoutPropertiesLoose(t,C),a=r(e,o),s=n(e,o),u=i(e,o);return(o.pure?function(e,t,r,n,i){var o,a,s,u,l,d=i.areStatesEqual,c=i.areOwnPropsEqual,f=i.areStatePropsEqual,p=!1;return function(i,y){var m,g,b,h;return p?(b=!c(y,a),h=!d(i,o,y,a),(o=i,a=y,b&&h)?(s=e(o,a),t.dependsOnOwnProps&&(u=t(n,a)),l=r(s,u,a)):b?(e.dependsOnOwnProps&&(s=e(o,a)),t.dependsOnOwnProps&&(u=t(n,a)),l=r(s,u,a)):(h&&(g=!f(m=e(o,a),s),s=m,g&&(l=r(s,u,a))),l)):(s=e(o=i,a=y),u=t(n,a),l=r(s,u,a),p=!0,l)}}:function(e,t,r,n){return function(i,o){return r(e(i,o),t(n,o),o)}})(a,s,u,e,o)}:p,function(e,t,r,n){void 0===n&&(n={});var i=n,o=i.pure,s=i.areStatesEqual,l=i.areOwnPropsEqual,c=i.areStatePropsEqual,p=i.areMergedPropsEqual,m=_objectWithoutPropertiesLoose(i,D),g=match(e,u,"mapStateToProps"),b=match(t,d,"mapDispatchToProps"),h=match(r,f,"mergeProps");return a(y,(0,v.Z)({methodName:"connect",getDisplayName:function(e){return"Connect("+e+")"},shouldHandleStateChanges:!!e,initMapStateToProps:g,initMapDispatchToProps:b,initMergeProps:h,pure:void 0===o||o,areStatesEqual:void 0===s?strictEqual:s,areOwnPropsEqual:void 0===l?shallowEqual:l,areStatePropsEqual:void 0===c?shallowEqual:c,areMergedPropsEqual:void 0===p?shallowEqual:p},m))});batch=r(967).unstable_batchedUpdates},5992:function(e,t,r){"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function _objectSpread2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(r),!0).forEach(function(t){!function(e,t,r){var n;(n=function(e,t){if("object"!=_typeof(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=_typeof(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==_typeof(n)?n:n+"")in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function formatProdErrorMessage(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}r.d(t,{md:function(){return applyMiddleware},DE:function(){return bindActionCreators},qC:function(){return compose},MT:function(){return createStore}});var n="function"==typeof Symbol&&Symbol.observable||"@@observable",randomString=function(){return Math.random().toString(36).substring(7).split("").join(".")},i={INIT:"@@redux/INIT"+randomString(),REPLACE:"@@redux/REPLACE"+randomString(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+randomString()}};function createStore(e,t,r){if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(formatProdErrorMessage(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw Error(formatProdErrorMessage(1));return r(createStore)(e,t)}if("function"!=typeof e)throw Error(formatProdErrorMessage(2));var o,a=e,s=t,u=[],l=u,d=!1;function ensureCanMutateNextListeners(){l===u&&(l=u.slice())}function getState(){if(d)throw Error(formatProdErrorMessage(3));return s}function subscribe(e){if("function"!=typeof e)throw Error(formatProdErrorMessage(4));if(d)throw Error(formatProdErrorMessage(5));var t=!0;return ensureCanMutateNextListeners(),l.push(e),function(){if(t){if(d)throw Error(formatProdErrorMessage(6));t=!1,ensureCanMutateNextListeners();var r=l.indexOf(e);l.splice(r,1),u=null}}}function dispatch(e){if(!function(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(e))throw Error(formatProdErrorMessage(7));if(void 0===e.type)throw Error(formatProdErrorMessage(8));if(d)throw Error(formatProdErrorMessage(9));try{d=!0,s=a(s,e)}finally{d=!1}for(var t=u=l,r=0;r<t.length;r++)(0,t[r])();return e}return dispatch({type:i.INIT}),(o={dispatch:dispatch,subscribe:subscribe,getState:getState,replaceReducer:function(e){if("function"!=typeof e)throw Error(formatProdErrorMessage(10));a=e,dispatch({type:i.REPLACE})}})[n]=function(){var e;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw Error(formatProdErrorMessage(11));function observeState(){e.next&&e.next(getState())}return observeState(),{unsubscribe:subscribe(observeState)}}})[n]=function(){return this},e},o}function bindActionCreator(e,t){return function(){return t(e.apply(this,arguments))}}function bindActionCreators(e,t){if("function"==typeof e)return bindActionCreator(e,t);if("object"!=typeof e||null===e)throw Error(formatProdErrorMessage(16));var r={};for(var n in e){var i=e[n];"function"==typeof i&&(r[n]=bindActionCreator(i,t))}return r}function compose(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce(function(e,t){return function(){return e(t.apply(void 0,arguments))}})}function applyMiddleware(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return function(){var r=e.apply(void 0,arguments),_dispatch=function(){throw Error(formatProdErrorMessage(15))},n={getState:r.getState,dispatch:function(){return _dispatch.apply(void 0,arguments)}},i=t.map(function(e){return e(n)});return _dispatch=compose.apply(void 0,i)(r.dispatch),_objectSpread2(_objectSpread2({},r),{},{dispatch:_dispatch})}}}},7448:function(e,t,r){"use strict";r.d(t,{I4:function(){return useCallback},Ye:function(){return i}});var n=r(5271);function useMemoOne(e,t){var r=(0,n.useState)(function(){return{inputs:t,result:e()}})[0],i=(0,n.useRef)(!0),o=(0,n.useRef)(r),a=i.current||t&&o.current.inputs&&function(e,t){if(e.length!==t.length)return!1;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(t,o.current.inputs)?o.current:{inputs:t,result:e()};return(0,n.useEffect)(function(){i.current=!1,o.current=a},[a]),a.result}var i=useMemoOne,useCallback=function(e,t){return useMemoOne(function(){return e},t)}},9911:function(e,t,r){"use strict";function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}r.d(t,{Z:function(){return _extends}})},2187:function(e,t,r){"use strict";function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _inheritsLoose(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,_setPrototypeOf(e,t)}r.d(t,{Z:function(){return _inheritsLoose}})},5134:function(e,t,r){"use strict";r.d(t,{cI:function(){return useForm}});var n=r(5271),isCheckBoxInput=e=>"checkbox"===e.type,isDateObject=e=>e instanceof Date,isNullOrUndefined=e=>null==e;let isObjectType=e=>"object"==typeof e;var isObject=e=>!isNullOrUndefined(e)&&!Array.isArray(e)&&isObjectType(e)&&!isDateObject(e),getEventValue=e=>isObject(e)&&e.target?isCheckBoxInput(e.target)?e.target.checked:e.target.value:e,getNodeParentName=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,isNameInFieldArray=(e,t)=>e.has(getNodeParentName(t)),isPlainObject=e=>{let t=e.constructor&&e.constructor.prototype;return isObject(t)&&t.hasOwnProperty("isPrototypeOf")},i="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function cloneObject(e){let t;let r=Array.isArray(e),n="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(!(i&&(e instanceof Blob||n))&&(r||isObject(e))))return e;else if(t=r?[]:{},r||isPlainObject(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=cloneObject(e[r]));else t=e;return t}var compact=e=>Array.isArray(e)?e.filter(Boolean):[],isUndefined=e=>void 0===e,get=(e,t,r)=>{if(!t||!isObject(e))return r;let n=compact(t.split(/[,[\].]+?/)).reduce((e,t)=>isNullOrUndefined(e)?e:e[t],e);return isUndefined(n)||n===e?isUndefined(e[t])?r:e[t]:n},isBoolean=e=>"boolean"==typeof e,isKey=e=>/^\w*$/.test(e),stringToPath=e=>compact(e.replace(/["|']|\]/g,"").split(/\.|\[/)),set=(e,t,r)=>{let n=-1,i=isKey(t)?[t]:stringToPath(t),o=i.length,a=o-1;for(;++n<o;){let t=i[n],o=r;if(n!==a){let r=e[t];o=isObject(r)||Array.isArray(r)?r:isNaN(+i[n+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=o,e=e[t]}};let o={BLUR:"blur",FOCUS_OUT:"focusout"},a={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},s={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};n.createContext(null);var getProxyFormState=(e,t,r,n=!0)=>{let i={defaultValues:t._defaultValues};for(let o in e)Object.defineProperty(i,o,{get:()=>(t._proxyFormState[o]!==a.all&&(t._proxyFormState[o]=!n||a.all),r&&(r[o]=!0),e[o])});return i};let u="undefined"!=typeof window?n.useLayoutEffect:n.useEffect;var isString=e=>"string"==typeof e,generateWatchOutput=(e,t,r,n,i)=>isString(e)?(n&&t.watch.add(e),get(r,e,i)):Array.isArray(e)?e.map(e=>(n&&t.watch.add(e),get(r,e))):(n&&(t.watchAll=!0),r),appendErrors=(e,t,r,n,i)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[n]:i||!0}}:{},convertToArrayPayload=e=>Array.isArray(e)?e:[e],createSubject=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},isPrimitive=e=>isNullOrUndefined(e)||!isObjectType(e);function deepEqual(e,t){if(isPrimitive(e)||isPrimitive(t))return e===t;if(isDateObject(e)&&isDateObject(t))return e.getTime()===t.getTime();let r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(let i of r){let r=e[i];if(!n.includes(i))return!1;if("ref"!==i){let e=t[i];if(isDateObject(r)&&isDateObject(e)||isObject(r)&&isObject(e)||Array.isArray(r)&&Array.isArray(e)?!deepEqual(r,e):r!==e)return!1}}return!0}var isEmptyObject=e=>isObject(e)&&!Object.keys(e).length,isFileInput=e=>"file"===e.type,isFunction=e=>"function"==typeof e,isHTMLElement=e=>{if(!i)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},isMultipleSelect=e=>"select-multiple"===e.type,isRadioInput=e=>"radio"===e.type,isRadioOrCheckbox=e=>isRadioInput(e)||isCheckBoxInput(e),live=e=>isHTMLElement(e)&&e.isConnected;function unset(e,t){let r=Array.isArray(t)?t:isKey(t)?[t]:stringToPath(t),n=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,n=0;for(;n<r;)e=isUndefined(e)?n++:e[t[n++]];return e}(e,r),i=r.length-1,o=r[i];return n&&delete n[o],0!==i&&(isObject(n)&&isEmptyObject(n)||Array.isArray(n)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!isUndefined(e[t]))return!1;return!0}(n))&&unset(e,r.slice(0,-1)),e}var objectHasFunction=e=>{for(let t in e)if(isFunction(e[t]))return!0;return!1};function markFieldsDirty(e,t={}){let r=Array.isArray(e);if(isObject(e)||r)for(let r in e)Array.isArray(e[r])||isObject(e[r])&&!objectHasFunction(e[r])?(t[r]=Array.isArray(e[r])?[]:{},markFieldsDirty(e[r],t[r])):isNullOrUndefined(e[r])||(t[r]=!0);return t}var getDirtyFields=(e,t)=>(function getDirtyFieldsFromDefaultValues(e,t,r){let n=Array.isArray(e);if(isObject(e)||n)for(let n in e)Array.isArray(e[n])||isObject(e[n])&&!objectHasFunction(e[n])?isUndefined(t)||isPrimitive(r[n])?r[n]=Array.isArray(e[n])?markFieldsDirty(e[n],[]):{...markFieldsDirty(e[n])}:getDirtyFieldsFromDefaultValues(e[n],isNullOrUndefined(t)?{}:t[n],r[n]):r[n]=!deepEqual(e[n],t[n]);return r})(e,t,markFieldsDirty(t));let l={value:!1,isValid:!1},d={value:!0,isValid:!0};var getCheckboxValue=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!isUndefined(e[0].attributes.value)?isUndefined(e[0].value)||""===e[0].value?d:{value:e[0].value,isValid:!0}:d:l}return l},getFieldValueAs=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:n})=>isUndefined(e)?e:t?""===e?NaN:e?+e:e:r&&isString(e)?new Date(e):n?n(e):e;let c={isValid:!1,value:null};var getRadioValue=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,c):c;function getFieldValue(e){let t=e.ref;return isFileInput(t)?t.files:isRadioInput(t)?getRadioValue(e.refs).value:isMultipleSelect(t)?[...t.selectedOptions].map(({value:e})=>e):isCheckBoxInput(t)?getCheckboxValue(e.refs).value:getFieldValueAs(isUndefined(t.value)?e.ref.value:t.value,e)}var getResolverOptions=(e,t,r,n)=>{let i={};for(let r of e){let e=get(t,r);e&&set(i,r,e._f)}return{criteriaMode:r,names:[...e],fields:i,shouldUseNativeValidation:n}},isRegex=e=>e instanceof RegExp,getRuleValue=e=>isUndefined(e)?e:isRegex(e)?e.source:isObject(e)?isRegex(e.value)?e.value.source:e.value:e,getValidationModes=e=>({isOnSubmit:!e||e===a.onSubmit,isOnBlur:e===a.onBlur,isOnChange:e===a.onChange,isOnAll:e===a.all,isOnTouch:e===a.onTouched});let f="AsyncFunction";var hasPromiseValidation=e=>!!e&&!!e.validate&&!!(isFunction(e.validate)&&e.validate.constructor.name===f||isObject(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===f)),hasValidation=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),isWatched=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let iterateFieldsByAction=(e,t,r,n)=>{for(let i of r||Object.keys(e)){let r=get(e,i);if(r){let{_f:e,...o}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],i)&&!n||e.ref&&t(e.ref,e.name)&&!n)return!0;if(iterateFieldsByAction(o,t))break}else if(isObject(o)&&iterateFieldsByAction(o,t))break}}};function schemaErrorLookup(e,t,r){let n=get(e,r);if(n||isKey(r))return{error:n,name:r};let i=r.split(".");for(;i.length;){let n=i.join("."),o=get(t,n),a=get(e,n);if(o&&!Array.isArray(o)&&r!==n)break;if(a&&a.type)return{name:n,error:a};i.pop()}return{name:r}}var shouldRenderFormState=(e,t,r,n)=>{r(e);let{name:i,...o}=e;return isEmptyObject(o)||Object.keys(o).length>=Object.keys(t).length||Object.keys(o).find(e=>t[e]===(!n||a.all))},shouldSubscribeByName=(e,t,r)=>!e||!t||e===t||convertToArrayPayload(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),skipValidation=(e,t,r,n,i)=>!i.isOnAll&&(!r&&i.isOnTouch?!(t||e):(r?n.isOnBlur:i.isOnBlur)?!e:(r?!n.isOnChange:!i.isOnChange)||e),unsetEmptyArray=(e,t)=>!compact(get(e,t)).length&&unset(e,t),updateFieldArrayRootError=(e,t,r)=>{let n=convertToArrayPayload(get(e,r));return set(n,"root",t[r]),set(e,r,n),e},isMessage=e=>isString(e);function getValidateError(e,t,r="validate"){if(isMessage(e)||Array.isArray(e)&&e.every(isMessage)||isBoolean(e)&&!e)return{type:r,message:isMessage(e)?e:"",ref:t}}var getValueAndMessage=e=>isObject(e)&&!isRegex(e)?e:{value:e,message:""},validateField=async(e,t,r,n,i,o)=>{let{ref:a,refs:u,required:l,maxLength:d,minLength:c,min:f,max:p,pattern:y,validate:m,name:g,valueAsNumber:b,mount:h}=e._f,v=get(r,g);if(!h||t.has(g))return{};let _=u?u[0]:a,setCustomValidity=e=>{i&&_.reportValidity&&(_.setCustomValidity(isBoolean(e)?"":e||""),_.reportValidity())},S={},O=isRadioInput(a),w=isCheckBoxInput(a),V=(b||isFileInput(a))&&isUndefined(a.value)&&isUndefined(v)||isHTMLElement(a)&&""===a.value||""===v||Array.isArray(v)&&!v.length,x=appendErrors.bind(null,g,n,S),getMinMaxMessage=(e,t,r,n=s.maxLength,i=s.minLength)=>{let o=e?t:r;S[g]={type:e?n:i,message:o,ref:a,...x(e?n:i,o)}};if(o?!Array.isArray(v)||!v.length:l&&(!(O||w)&&(V||isNullOrUndefined(v))||isBoolean(v)&&!v||w&&!getCheckboxValue(u).isValid||O&&!getRadioValue(u).isValid)){let{value:e,message:t}=isMessage(l)?{value:!!l,message:l}:getValueAndMessage(l);if(e&&(S[g]={type:s.required,message:t,ref:_,...x(s.required,t)},!n))return setCustomValidity(t),S}if(!V&&(!isNullOrUndefined(f)||!isNullOrUndefined(p))){let e,t;let r=getValueAndMessage(p),i=getValueAndMessage(f);if(isNullOrUndefined(v)||isNaN(v)){let n=a.valueAsDate||new Date(v),convertTimeToDate=e=>new Date(new Date().toDateString()+" "+e),o="time"==a.type,s="week"==a.type;isString(r.value)&&v&&(e=o?convertTimeToDate(v)>convertTimeToDate(r.value):s?v>r.value:n>new Date(r.value)),isString(i.value)&&v&&(t=o?convertTimeToDate(v)<convertTimeToDate(i.value):s?v<i.value:n<new Date(i.value))}else{let n=a.valueAsNumber||(v?+v:v);isNullOrUndefined(r.value)||(e=n>r.value),isNullOrUndefined(i.value)||(t=n<i.value)}if((e||t)&&(getMinMaxMessage(!!e,r.message,i.message,s.max,s.min),!n))return setCustomValidity(S[g].message),S}if((d||c)&&!V&&(isString(v)||o&&Array.isArray(v))){let e=getValueAndMessage(d),t=getValueAndMessage(c),r=!isNullOrUndefined(e.value)&&v.length>+e.value,i=!isNullOrUndefined(t.value)&&v.length<+t.value;if((r||i)&&(getMinMaxMessage(r,e.message,t.message),!n))return setCustomValidity(S[g].message),S}if(y&&!V&&isString(v)){let{value:e,message:t}=getValueAndMessage(y);if(isRegex(e)&&!v.match(e)&&(S[g]={type:s.pattern,message:t,ref:a,...x(s.pattern,t)},!n))return setCustomValidity(t),S}if(m){if(isFunction(m)){let e=await m(v,r),t=getValidateError(e,_);if(t&&(S[g]={...t,...x(s.validate,t.message)},!n))return setCustomValidity(t.message),S}else if(isObject(m)){let e={};for(let t in m){if(!isEmptyObject(e)&&!n)break;let i=getValidateError(await m[t](v,r),_,t);i&&(e={...i,...x(t,i.message)},setCustomValidity(i.message),n&&(S[g]=e))}if(!isEmptyObject(e)&&(S[g]={ref:_,...e},!n))return S}}return setCustomValidity(!0),S};let p={mode:a.onSubmit,reValidateMode:a.onChange,shouldFocusError:!0};function useForm(e={}){let t=n.useRef(void 0),r=n.useRef(void 0),[s,l]=n.useState({isDirty:!1,isValidating:!1,isLoading:isFunction(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:isFunction(e.defaultValues)?void 0:e.defaultValues});!t.current&&(t.current={...e.formControl?e.formControl:function(e={}){let t,r={...p,...e},n={submitCount:0,isDirty:!1,isReady:!1,isLoading:isFunction(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},s={},u=(isObject(r.defaultValues)||isObject(r.values))&&cloneObject(r.defaultValues||r.values)||{},l=r.shouldUnregister?{}:cloneObject(u),d={action:!1,mount:!1,watch:!1},c={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},f=0,y={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},m={...y},g={array:createSubject(),state:createSubject()},b=r.criteriaMode===a.all,debounce=e=>t=>{clearTimeout(f),f=setTimeout(e,t)},_setValid=async e=>{if(!r.disabled&&(y.isValid||m.isValid||e)){let e=r.resolver?isEmptyObject((await _runSchema()).errors):await executeBuiltInValidation(s,!0);e!==n.isValid&&g.state.next({isValid:e})}},_updateIsValidating=(e,t)=>{!r.disabled&&(y.isValidating||y.validatingFields||m.isValidating||m.validatingFields)&&((e||Array.from(c.mount)).forEach(e=>{e&&(t?set(n.validatingFields,e,t):unset(n.validatingFields,e))}),g.state.next({validatingFields:n.validatingFields,isValidating:!isEmptyObject(n.validatingFields)}))},updateErrors=(e,t)=>{set(n.errors,e,t),g.state.next({errors:n.errors})},updateValidAndValue=(e,t,r,n)=>{let i=get(s,e);if(i){let o=get(l,e,isUndefined(r)?get(u,e):r);isUndefined(o)||n&&n.defaultChecked||t?set(l,e,t?o:getFieldValue(i._f)):setFieldValue(e,o),d.mount&&_setValid()}},updateTouchAndDirty=(e,t,i,o,a)=>{let s=!1,l=!1,d={name:e};if(!r.disabled){if(!i||o){(y.isDirty||m.isDirty)&&(l=n.isDirty,n.isDirty=d.isDirty=_getDirty(),s=l!==d.isDirty);let r=deepEqual(get(u,e),t);l=!!get(n.dirtyFields,e),r?unset(n.dirtyFields,e):set(n.dirtyFields,e,!0),d.dirtyFields=n.dirtyFields,s=s||(y.dirtyFields||m.dirtyFields)&&!r!==l}if(i){let t=get(n.touchedFields,e);t||(set(n.touchedFields,e,i),d.touchedFields=n.touchedFields,s=s||(y.touchedFields||m.touchedFields)&&t!==i)}s&&a&&g.state.next(d)}return s?d:{}},shouldRenderByError=(e,i,o,a)=>{let s=get(n.errors,e),u=(y.isValid||m.isValid)&&isBoolean(i)&&n.isValid!==i;if(r.delayError&&o?(t=debounce(()=>updateErrors(e,o)))(r.delayError):(clearTimeout(f),t=null,o?set(n.errors,e,o):unset(n.errors,e)),(o?!deepEqual(s,o):s)||!isEmptyObject(a)||u){let t={...a,...u&&isBoolean(i)?{isValid:i}:{},errors:n.errors,name:e};n={...n,...t},g.state.next(t)}},_runSchema=async e=>{_updateIsValidating(e,!0);let t=await r.resolver(l,r.context,getResolverOptions(e||c.mount,s,r.criteriaMode,r.shouldUseNativeValidation));return _updateIsValidating(e),t},executeSchemaAndUpdateState=async e=>{let{errors:t}=await _runSchema(e);if(e)for(let r of e){let e=get(t,r);e?set(n.errors,r,e):unset(n.errors,r)}else n.errors=t;return t},executeBuiltInValidation=async(e,t,i={valid:!0})=>{for(let o in e){let a=e[o];if(a){let{_f:e,...s}=a;if(e){let s=c.array.has(e.name),u=a._f&&hasPromiseValidation(a._f);u&&y.validatingFields&&_updateIsValidating([o],!0);let d=await validateField(a,c.disabled,l,b,r.shouldUseNativeValidation&&!t,s);if(u&&y.validatingFields&&_updateIsValidating([o]),d[e.name]&&(i.valid=!1,t))break;t||(get(d,e.name)?s?updateFieldArrayRootError(n.errors,d,e.name):set(n.errors,e.name,d[e.name]):unset(n.errors,e.name))}isEmptyObject(s)||await executeBuiltInValidation(s,t,i)}}return i.valid},_getDirty=(e,t)=>!r.disabled&&(e&&t&&set(l,e,t),!deepEqual(getValues(),u)),_getWatch=(e,t,r)=>generateWatchOutput(e,c,{...d.mount?l:isUndefined(t)?u:isString(e)?{[e]:t}:t},r,t),setFieldValue=(e,t,r={})=>{let n=get(s,e),i=t;if(n){let r=n._f;r&&(r.disabled||set(l,e,getFieldValueAs(t,r)),i=isHTMLElement(r.ref)&&isNullOrUndefined(t)?"":t,isMultipleSelect(r.ref)?[...r.ref.options].forEach(e=>e.selected=i.includes(e.value)):r.refs?isCheckBoxInput(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(i)?e.checked=!!i.find(t=>t===e.value):e.checked=i===e.value||!!i)}):r.refs.forEach(e=>e.checked=e.value===i):isFileInput(r.ref)?r.ref.value="":(r.ref.value=i,r.ref.type||g.state.next({name:e,values:cloneObject(l)})))}(r.shouldDirty||r.shouldTouch)&&updateTouchAndDirty(e,i,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&trigger(e)},setValues=(e,t,r)=>{for(let n in t){if(!t.hasOwnProperty(n))return;let i=t[n],o=`${e}.${n}`,a=get(s,o);(c.array.has(e)||isObject(i)||a&&!a._f)&&!isDateObject(i)?setValues(o,i,r):setFieldValue(o,i,r)}},setValue=(e,t,r={})=>{let i=get(s,e),o=c.array.has(e),a=cloneObject(t);set(l,e,a),o?(g.array.next({name:e,values:cloneObject(l)}),(y.isDirty||y.dirtyFields||m.isDirty||m.dirtyFields)&&r.shouldDirty&&g.state.next({name:e,dirtyFields:getDirtyFields(u,l),isDirty:_getDirty(e,a)})):!i||i._f||isNullOrUndefined(a)?setFieldValue(e,a,r):setValues(e,a,r),isWatched(e,c)&&g.state.next({...n}),g.state.next({name:d.mount?e:void 0,values:cloneObject(l)})},onChange=async e=>{d.mount=!0;let i=e.target,a=i.name,u=!0,f=get(s,a),_updateIsFieldValueUpdated=e=>{u=Number.isNaN(e)||isDateObject(e)&&isNaN(e.getTime())||deepEqual(e,get(l,a,e))},p=getValidationModes(r.mode),h=getValidationModes(r.reValidateMode);if(f){let d,v;let _=i.type?getFieldValue(f._f):getEventValue(e),S=e.type===o.BLUR||e.type===o.FOCUS_OUT,O=!hasValidation(f._f)&&!r.resolver&&!get(n.errors,a)&&!f._f.deps||skipValidation(S,get(n.touchedFields,a),n.isSubmitted,h,p),w=isWatched(a,c,S);set(l,a,_),S?(f._f.onBlur&&f._f.onBlur(e),t&&t(0)):f._f.onChange&&f._f.onChange(e);let V=updateTouchAndDirty(a,_,S),x=!isEmptyObject(V)||w;if(S||g.state.next({name:a,type:e.type,values:cloneObject(l)}),O)return(y.isValid||m.isValid)&&("onBlur"===r.mode?S&&_setValid():S||_setValid()),x&&g.state.next({name:a,...w?{}:V});if(!S&&w&&g.state.next({...n}),r.resolver){let{errors:e}=await _runSchema([a]);if(_updateIsFieldValueUpdated(_),u){let t=schemaErrorLookup(n.errors,s,a),r=schemaErrorLookup(e,s,t.name||a);d=r.error,a=r.name,v=isEmptyObject(e)}}else _updateIsValidating([a],!0),d=(await validateField(f,c.disabled,l,b,r.shouldUseNativeValidation))[a],_updateIsValidating([a]),_updateIsFieldValueUpdated(_),u&&(d?v=!1:(y.isValid||m.isValid)&&(v=await executeBuiltInValidation(s,!0)));u&&(f._f.deps&&trigger(f._f.deps),shouldRenderByError(a,v,d,V))}},_focusInput=(e,t)=>{if(get(n.errors,t)&&e.focus)return e.focus(),1},trigger=async(e,t={})=>{let i,o;let a=convertToArrayPayload(e);if(r.resolver){let t=await executeSchemaAndUpdateState(isUndefined(e)?e:a);i=isEmptyObject(t),o=e?!a.some(e=>get(t,e)):i}else e?((o=(await Promise.all(a.map(async e=>{let t=get(s,e);return await executeBuiltInValidation(t&&t._f?{[e]:t}:t)}))).every(Boolean))||n.isValid)&&_setValid():o=i=await executeBuiltInValidation(s);return g.state.next({...!isString(e)||(y.isValid||m.isValid)&&i!==n.isValid?{}:{name:e},...r.resolver||!e?{isValid:i}:{},errors:n.errors}),t.shouldFocus&&!o&&iterateFieldsByAction(s,_focusInput,e?a:c.mount),o},getValues=e=>{let t={...d.mount?l:u};return isUndefined(e)?t:isString(e)?get(t,e):e.map(e=>get(t,e))},getFieldState=(e,t)=>({invalid:!!get((t||n).errors,e),isDirty:!!get((t||n).dirtyFields,e),error:get((t||n).errors,e),isValidating:!!get(n.validatingFields,e),isTouched:!!get((t||n).touchedFields,e)}),setError=(e,t,r)=>{let i=(get(s,e,{_f:{}})._f||{}).ref,o=get(n.errors,e)||{},{ref:a,message:u,type:l,...d}=o;set(n.errors,e,{...d,...t,ref:i}),g.state.next({name:e,errors:n.errors,isValid:!1}),r&&r.shouldFocus&&i&&i.focus&&i.focus()},_subscribe=e=>g.state.subscribe({next:t=>{shouldSubscribeByName(e.name,t.name,e.exact)&&shouldRenderFormState(t,e.formState||y,_setFormState,e.reRenderRoot)&&e.callback({values:{...l},...n,...t})}}).unsubscribe,unregister=(e,t={})=>{for(let i of e?convertToArrayPayload(e):c.mount)c.mount.delete(i),c.array.delete(i),t.keepValue||(unset(s,i),unset(l,i)),t.keepError||unset(n.errors,i),t.keepDirty||unset(n.dirtyFields,i),t.keepTouched||unset(n.touchedFields,i),t.keepIsValidating||unset(n.validatingFields,i),r.shouldUnregister||t.keepDefaultValue||unset(u,i);g.state.next({values:cloneObject(l)}),g.state.next({...n,...t.keepDirty?{isDirty:_getDirty()}:{}}),t.keepIsValid||_setValid()},_setDisabledField=({disabled:e,name:t})=>{(isBoolean(e)&&d.mount||e||c.disabled.has(t))&&(e?c.disabled.add(t):c.disabled.delete(t))},register=(e,t={})=>{let n=get(s,e),i=isBoolean(t.disabled)||isBoolean(r.disabled);return set(s,e,{...n||{},_f:{...n&&n._f?n._f:{ref:{name:e}},name:e,mount:!0,...t}}),c.mount.add(e),n?_setDisabledField({disabled:isBoolean(t.disabled)?t.disabled:r.disabled,name:e}):updateValidAndValue(e,!0,t.value),{...i?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:getRuleValue(t.min),max:getRuleValue(t.max),minLength:getRuleValue(t.minLength),maxLength:getRuleValue(t.maxLength),pattern:getRuleValue(t.pattern)}:{},name:e,onChange,onBlur:onChange,ref:i=>{if(i){register(e,t),n=get(s,e);let r=isUndefined(i.value)&&i.querySelectorAll&&i.querySelectorAll("input,select,textarea")[0]||i,o=isRadioOrCheckbox(r),a=n._f.refs||[];(o?a.find(e=>e===r):r===n._f.ref)||(set(s,e,{_f:{...n._f,...o?{refs:[...a.filter(live),r,...Array.isArray(get(u,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),updateValidAndValue(e,!1,void 0,r))}else(n=get(s,e,{}))._f&&(n._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(isNameInFieldArray(c.array,e)&&d.action)&&c.unMount.add(e)}}},_focusError=()=>r.shouldFocusError&&iterateFieldsByAction(s,_focusInput,c.mount),handleSubmit=(e,t)=>async i=>{let o;i&&(i.preventDefault&&i.preventDefault(),i.persist&&i.persist());let a=cloneObject(l);if(g.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await _runSchema();n.errors=e,a=t}else await executeBuiltInValidation(s);if(c.disabled.size)for(let e of c.disabled)set(a,e,void 0);if(unset(n.errors,"root"),isEmptyObject(n.errors)){g.state.next({errors:{}});try{await e(a,i)}catch(e){o=e}}else t&&await t({...n.errors},i),_focusError(),setTimeout(_focusError);if(g.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:isEmptyObject(n.errors)&&!o,submitCount:n.submitCount+1,errors:n.errors}),o)throw o},_reset=(e,t={})=>{let o=e?cloneObject(e):u,a=cloneObject(o),f=isEmptyObject(e),p=f?u:a;if(t.keepDefaultValues||(u=o),!t.keepValues){if(t.keepDirtyValues){let e=new Set([...c.mount,...Object.keys(getDirtyFields(u,l))]);for(let t of Array.from(e))get(n.dirtyFields,t)?set(p,t,get(l,t)):setValue(t,get(p,t))}else{if(i&&isUndefined(e))for(let e of c.mount){let t=get(s,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(isHTMLElement(e)){let t=e.closest("form");if(t){t.reset();break}}}}for(let e of c.mount)setValue(e,get(p,e))}l=cloneObject(p),g.array.next({values:{...p}}),g.state.next({values:{...p}})}c={mount:t.keepDirtyValues?c.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},d.mount=!y.isValid||!!t.keepIsValid||!!t.keepDirtyValues,d.watch=!!r.shouldUnregister,g.state.next({submitCount:t.keepSubmitCount?n.submitCount:0,isDirty:!f&&(t.keepDirty?n.isDirty:!!(t.keepDefaultValues&&!deepEqual(e,u))),isSubmitted:!!t.keepIsSubmitted&&n.isSubmitted,dirtyFields:f?{}:t.keepDirtyValues?t.keepDefaultValues&&l?getDirtyFields(u,l):n.dirtyFields:t.keepDefaultValues&&e?getDirtyFields(u,e):t.keepDirty?n.dirtyFields:{},touchedFields:t.keepTouched?n.touchedFields:{},errors:t.keepErrors?n.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&n.isSubmitSuccessful,isSubmitting:!1})},reset=(e,t)=>_reset(isFunction(e)?e(l):e,t),_setFormState=e=>{n={...n,...e}},h={control:{register,unregister,getFieldState,handleSubmit,setError,_subscribe,_runSchema,_getWatch,_getDirty,_setValid,_setFieldArray:(e,t=[],i,o,a=!0,c=!0)=>{if(o&&i&&!r.disabled){if(d.action=!0,c&&Array.isArray(get(s,e))){let t=i(get(s,e),o.argA,o.argB);a&&set(s,e,t)}if(c&&Array.isArray(get(n.errors,e))){let t=i(get(n.errors,e),o.argA,o.argB);a&&set(n.errors,e,t),unsetEmptyArray(n.errors,e)}if((y.touchedFields||m.touchedFields)&&c&&Array.isArray(get(n.touchedFields,e))){let t=i(get(n.touchedFields,e),o.argA,o.argB);a&&set(n.touchedFields,e,t)}(y.dirtyFields||m.dirtyFields)&&(n.dirtyFields=getDirtyFields(u,l)),g.state.next({name:e,isDirty:_getDirty(e,t),dirtyFields:n.dirtyFields,errors:n.errors,isValid:n.isValid})}else set(l,e,t)},_setDisabledField,_setErrors:e=>{n.errors=e,g.state.next({errors:n.errors,isValid:!1})},_getFieldArray:e=>compact(get(d.mount?l:u,e,r.shouldUnregister?get(u,e,[]):[])),_reset,_resetDefaultValues:()=>isFunction(r.defaultValues)&&r.defaultValues().then(e=>{reset(e,r.resetOptions),g.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of c.unMount){let t=get(s,e);t&&(t._f.refs?t._f.refs.every(e=>!live(e)):!live(t._f.ref))&&unregister(e)}c.unMount=new Set},_disableForm:e=>{isBoolean(e)&&(g.state.next({disabled:e}),iterateFieldsByAction(s,(t,r)=>{let n=get(s,r);n&&(t.disabled=n._f.disabled||e,Array.isArray(n._f.refs)&&n._f.refs.forEach(t=>{t.disabled=n._f.disabled||e}))},0,!1))},_subjects:g,_proxyFormState:y,get _fields(){return s},get _formValues(){return l},get _state(){return d},set _state(value){d=value},get _defaultValues(){return u},get _names(){return c},set _names(value){c=value},get _formState(){return n},get _options(){return r},set _options(value){r={...r,...value}}},subscribe:e=>(d.mount=!0,m={...m,...e.formState},_subscribe({...e,formState:m})),trigger,register,handleSubmit,watch:(e,t)=>isFunction(e)?g.state.subscribe({next:r=>e(_getWatch(void 0,t),r)}):_getWatch(e,t,!0),setValue,getValues,reset,resetField:(e,t={})=>{get(s,e)&&(isUndefined(t.defaultValue)?setValue(e,cloneObject(get(u,e))):(setValue(e,t.defaultValue),set(u,e,cloneObject(t.defaultValue))),t.keepTouched||unset(n.touchedFields,e),t.keepDirty||(unset(n.dirtyFields,e),n.isDirty=t.defaultValue?_getDirty(e,cloneObject(get(u,e))):_getDirty()),!t.keepError&&(unset(n.errors,e),y.isValid&&_setValid()),g.state.next({...n}))},clearErrors:e=>{e&&convertToArrayPayload(e).forEach(e=>unset(n.errors,e)),g.state.next({errors:e?n.errors:{}})},unregister,setError,setFocus:(e,t={})=>{let r=get(s,e),n=r&&r._f;if(n){let e=n.refs?n.refs[0]:n.ref;e.focus&&(e.focus(),t.shouldSelect&&isFunction(e.select)&&e.select())}},getFieldState};return{...h,formControl:h}}(e),formState:s},e.formControl&&e.defaultValues&&!isFunction(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));let d=t.current.control;return d._options=e,u(()=>{let e=d._subscribe({formState:d._proxyFormState,callback:()=>l({...d._formState}),reRenderRoot:!0});return l(e=>({...e,isReady:!0})),d._formState.isReady=!0,e},[d]),n.useEffect(()=>d._disableForm(e.disabled),[d,e.disabled]),n.useEffect(()=>{e.mode&&(d._options.mode=e.mode),e.reValidateMode&&(d._options.reValidateMode=e.reValidateMode),e.errors&&!isEmptyObject(e.errors)&&d._setErrors(e.errors)},[d,e.errors,e.mode,e.reValidateMode]),n.useEffect(()=>{e.shouldUnregister&&d._subjects.state.next({values:d._getWatch()})},[d,e.shouldUnregister]),n.useEffect(()=>{if(d._proxyFormState.isDirty){let e=d._getDirty();e!==s.isDirty&&d._subjects.state.next({isDirty:e})}},[d,s.isDirty]),n.useEffect(()=>{e.values&&!deepEqual(e.values,r.current)?(d._reset(e.values,d._options.resetOptions),r.current=e.values,l(e=>({...e}))):d._resetDefaultValues()},[d,e.values]),n.useEffect(()=>{d._state.mount||(d._setValid(),d._state.mount=!0),d._state.watch&&(d._state.watch=!1,d._subjects.state.next({...d._formState})),d._removeUnmounted()}),t.current.formState=getProxyFormState(s,d),t.current}}}]);
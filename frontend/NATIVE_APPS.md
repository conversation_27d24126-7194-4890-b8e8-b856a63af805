# Native Applications Setup

This document explains how to build native applications for macOS (desktop) and iOS from your Cryptodo frontend.

## Overview

We've set up two native app solutions:
- **Tauri** for macOS desktop application
- **Capacitor** for iOS mobile application

## Prerequisites

### For macOS Desktop (Tauri)
- Rust (install from https://rustup.rs/)
- Xcode Command Line Tools: `xcode-select --install`

### For iOS (Capacitor)
- Xcode (from Mac App Store)
- iOS Simulator or physical iOS device
- CocoaPods: `sudo gem install cocoapods`

## macOS Desktop App (Tauri)

### Development
```bash
# Start development mode (opens desktop app with hot reload)
pnpm tauri:dev
```

### Building for Production
```bash
# Build optimized desktop app for production (connects to https://cryptodo.dsserv.de)
pnpm tauri:build

# Build debug version for production (faster compilation)
pnpm tauri:build:debug

# Build for local development (connects to localhost backend)
pnpm tauri:build:local
```

The built app will be located in:
- `src-tauri/target/release/bundle/macos/Cryptodo.app` (production)
- `src-tauri/target/debug/bundle/macos/Cryptodo.app` (debug)

### Configuration
- Main config: `src-tauri/tauri.conf.json`
- Window size: 1200x800 (minimum 800x600)
- App identifier: `com.cryptodo.app`

## iOS App (Capacitor)

### Initial Setup
```bash
# Install CocoaPods if not already installed
sudo gem install cocoapods

# Sync web assets and native dependencies
pnpm cap:sync
```

### Development
```bash
# Build web assets and open Xcode (local development)
pnpm ios:build

# Build for production backend (connects to https://cryptodo.dsserv.de)
pnpm ios:build:production

# Or step by step:
pnpm build          # Build Next.js app
pnpm cap:sync       # Sync assets to iOS
pnpm cap:open:ios   # Open Xcode
```

### Building for Production
1. Open Xcode with `pnpm cap:open:ios`
2. Select your development team in project settings
3. Choose target device (simulator or physical device)
4. Click the play button to build and run
5. For App Store: Product → Archive

### Configuration
- Main config: `capacitor.config.ts`
- App identifier: `com.cryptodo.app`
- App name: `Cryptodo`

## Important Notes

### Next.js Configuration
The app is configured for static export (`output: 'export'`) which is required for both Tauri and Capacitor. This means:
- No server-side rendering
- No API routes
- Images must use `unoptimized: true`

### GraphQL Backend
The apps are configured to connect to different backends:

**Development Mode (`pnpm tauri:dev`)**
- Connects to: `http://localhost:4544/graphql`
- Uses: `.env.local` configuration

**Production Build (`pnpm tauri:build`)**
- Connects to: `https://cryptodo.dsserv.de/graphql`
- Uses: `.env.tauri.production` configuration

**Local Build (`pnpm tauri:build:local`)**
- Connects to: `http://localhost:4544/graphql`
- Uses: current `.env.local` configuration

### Authentication
Both native apps will use the same authentication flow as the web app:
- Google OAuth
- Apple Sign In
- Email/password

### File Structure
```
frontend/
├── src-tauri/          # Tauri desktop app configuration
├── ios/                # iOS app (generated by Capacitor)
├── capacitor.config.ts # Capacitor configuration
├── out/                # Built web assets (used by both)
└── NATIVE_APPS.md      # This file
```

## Troubleshooting

### Tauri Issues
- If Rust is not found: Install from https://rustup.rs/
- If build fails: Try `cargo clean` in `src-tauri/` directory

### iOS Issues
- If CocoaPods fails: Run `pod install` in `ios/App/` directory
- If Xcode can't find project: Make sure you opened the `.xcworkspace` file
- If app crashes: Check the Xcode console for error messages

### General Issues
- If assets are missing: Run `pnpm build` then `pnpm cap:sync`
- If GraphQL fails: Check backend URL in Apollo client configuration

## Next Steps

1. **Test the desktop app**: Run `pnpm tauri:dev`
2. **Test the iOS app**: Run `pnpm ios:build`
3. **Customize icons**: Replace icons in `src-tauri/icons/` and `ios/App/App/Assets.xcassets/`
4. **Configure app metadata**: Update bundle identifiers, descriptions, etc.
5. **Set up code signing**: For distribution on Mac App Store or iOS App Store

## Distribution

### macOS
- Direct distribution: Share the `.app` file
- Mac App Store: Requires Apple Developer account and notarization

### iOS
- TestFlight: For beta testing (requires Apple Developer account)
- App Store: For public distribution (requires Apple Developer account)

{"name": "cryptodo-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 4545", "build": "next build", "start": "next start", "lint": "next lint", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "cp .env.tauri.production .env.local && pnpm build && tauri build", "tauri:build:debug": "cp .env.tauri.production .env.local && pnpm build && tauri build --debug", "tauri:build:local": "tauri build", "cap": "cap", "cap:add:ios": "cap add ios", "cap:sync": "cap sync", "cap:open:ios": "cap open ios", "ios:build": "pnpm build && cap sync && cap open ios"}, "dependencies": {"@apollo/client": "^3.8.0", "@react-oauth/google": "^0.12.2", "@types/js-cookie": "^3.0.6", "@types/node": "^20.4.5", "@types/react": "^18.2.18", "@types/react-dom": "^18.2.7", "graphql": "^16.7.1", "js-cookie": "^3.0.5", "next": "^13.4.12", "quill-image-drop-module": "^1.0.3", "quill-image-resize-module": "^3.0.0", "react": "^18.2.0", "react-apple-signin-auth": "^1.1.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "react-markdown": "^8.0.7", "react-quill": "^2.0.0", "typescript": "^5.1.6"}, "devDependencies": {"@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/ios": "^7.2.0", "@tauri-apps/api": "^2.5.0", "@tauri-apps/cli": "^2.5.0", "@types/react-beautiful-dnd": "^13.1.4", "eslint": "^8.46.0", "eslint-config-next": "^13.4.12", "sass": "^1.64.2"}}
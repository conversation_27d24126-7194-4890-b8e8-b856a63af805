#!/bin/bash

# Build Tauri app for production with correct environment variables

echo "🚀 Building Cryptodo for production..."

# Backup current .env.local if it exists
if [ -f .env.local ]; then
    echo "📦 Backing up current .env.local..."
    cp .env.local .env.local.backup
fi

# Copy production environment variables
echo "🔧 Setting up production environment..."
cp .env.tauri.production .env.local

# Build the Next.js app with production settings
echo "🏗️  Building Next.js app..."
pnpm build

# Build the Tauri app
echo "📱 Building Tauri desktop app..."
pnpm tauri build

# Restore original .env.local if backup exists
if [ -f .env.local.backup ]; then
    echo "🔄 Restoring original .env.local..."
    mv .env.local.backup .env.local
else
    echo "🧹 Cleaning up production .env.local..."
    rm .env.local
fi

echo "✅ Production build complete!"
echo "📍 App location: src-tauri/target/release/bundle/macos/Cryptodo.app"

# Connecting Tauri App to Production Backend

## ✅ Problem Solved

Your Tauri desktop app is now configured to connect to your production backend at `https://cryptodo.dsserv.de/graphql` when built for production.

## 🔧 What Was Configured

### 1. Environment Configuration
- **Created**: `frontend/.env.tauri.production` with production GraphQL URL
- **Updated**: Build scripts to use production environment for builds
- **Preserved**: Development environment for local testing

### 2. Build Scripts Updated
```bash
# For production (connects to https://cryptodo.dsserv.de)
pnpm tauri:build          # Full production build
pnpm tauri:build:debug    # Debug production build

# For local development (connects to localhost)
pnpm tauri:dev           # Development with hot reload
pnpm tauri:build:local   # Local build
```

### 3. Backend CORS Configuration
✅ Your backend already supports Tauri apps:
- Allows requests with no origin (perfect for desktop apps)
- Configured for `https://cryptodo.dsserv.de`
- Supports credentials and proper headers

## 🚀 How to Build for Production

### Quick Method
```bash
cd frontend
pnpm tauri:build
```

### What Happens
1. Copies production environment variables
2. Builds Next.js with production GraphQL endpoint
3. Builds Tauri desktop app
4. Restores original environment
5. Creates app at: `src-tauri/target/release/bundle/macos/Cryptodo.app`

## 📱 iOS App Production

For iOS apps connecting to production:
```bash
cd frontend
pnpm ios:build:production
```

## 🔍 Environment Details

### Development (.env.local)
```
NEXT_PUBLIC_GRAPHQL_URL=http://localhost:4544/graphql
NEXT_PUBLIC_GOOGLE_CLIENT_ID=597526416965-7lblao7rje600j1emmegrv47krt0om9t.apps.googleusercontent.com
NODE_ENV=development
```

### Production (.env.tauri.production)
```
NEXT_PUBLIC_GRAPHQL_URL=https://cryptodo.dsserv.de/graphql
NEXT_PUBLIC_GOOGLE_CLIENT_ID=597526416965-7lblao7rje600j1emmegrv47krt0om9t.apps.googleusercontent.com
NODE_ENV=production
```

## 🔐 Authentication

The production app will use:
- **Google OAuth**: Same client ID as web app
- **JWT Tokens**: Stored in cookies, sent to production backend
- **CORS**: Backend allows desktop app requests

## ✅ Testing

### 1. Test Production Build
```bash
cd frontend
pnpm tauri:build
```

### 2. Run the Built App
```bash
open src-tauri/target/release/bundle/macos/Cryptodo.app
```

### 3. Verify Connection
- App should connect to `https://cryptodo.dsserv.de/graphql`
- Authentication should work with production backend
- All features should function normally

## 🛠️ Troubleshooting

### If App Can't Connect
1. **Check Backend**: Ensure `https://cryptodo.dsserv.de/graphql` is accessible
2. **Check CORS**: Backend should allow requests without origin
3. **Check SSL**: Ensure SSL certificate is valid

### If Authentication Fails
1. **Google OAuth**: Verify client ID matches production
2. **JWT**: Check token storage and transmission
3. **Cookies**: Ensure cookies work in desktop app

### If Build Fails
1. **Rust**: Ensure Rust is installed (`rustup.rs`)
2. **Dependencies**: Run `pnpm install` in frontend
3. **Environment**: Check `.env.tauri.production` exists

## 📦 Distribution

The built app (`Cryptodo.app`) can be:
- **Shared directly**: Copy to other Macs
- **Notarized**: For distribution outside App Store
- **App Store**: Submit to Mac App Store

## 🔄 Switching Environments

### Back to Development
```bash
cd frontend
pnpm tauri:dev  # Uses localhost backend
```

### Local Production Build
```bash
cd frontend
pnpm tauri:build:local  # Uses current .env.local
```

## 📋 Summary

✅ **Production builds** → Connect to `https://cryptodo.dsserv.de/graphql`  
✅ **Development mode** → Connect to `http://localhost:4544/graphql`  
✅ **CORS configured** → Backend accepts desktop app requests  
✅ **Authentication** → Works with production Google OAuth  
✅ **Build scripts** → Automated environment switching  

Your Tauri app is now ready for production use! 🎉
